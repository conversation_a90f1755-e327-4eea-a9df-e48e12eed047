# coding: utf-8

"""
    Kubernetes

    No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)  # noqa: E501

    The version of the OpenAPI document: release-1.33
    Generated by: https://openapi-generator.tech
"""


import pprint
import re  # noqa: F401

import six

from kubernetes.client.configuration import Configuration


class V1alpha3ResourceClaimStatus(object):
    """NOTE: This class is auto generated by OpenAPI Generator.
    Ref: https://openapi-generator.tech

    Do not edit the class manually.
    """

    """
    Attributes:
      openapi_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    openapi_types = {
        'allocation': 'V1alpha3AllocationResult',
        'devices': 'list[V1alpha3AllocatedDeviceStatus]',
        'reserved_for': 'list[V1alpha3ResourceClaimConsumerReference]'
    }

    attribute_map = {
        'allocation': 'allocation',
        'devices': 'devices',
        'reserved_for': 'reservedFor'
    }

    def __init__(self, allocation=None, devices=None, reserved_for=None, local_vars_configuration=None):  # noqa: E501
        """V1alpha3ResourceClaimStatus - a model defined in OpenAPI"""  # noqa: E501
        if local_vars_configuration is None:
            local_vars_configuration = Configuration()
        self.local_vars_configuration = local_vars_configuration

        self._allocation = None
        self._devices = None
        self._reserved_for = None
        self.discriminator = None

        if allocation is not None:
            self.allocation = allocation
        if devices is not None:
            self.devices = devices
        if reserved_for is not None:
            self.reserved_for = reserved_for

    @property
    def allocation(self):
        """Gets the allocation of this V1alpha3ResourceClaimStatus.  # noqa: E501


        :return: The allocation of this V1alpha3ResourceClaimStatus.  # noqa: E501
        :rtype: V1alpha3AllocationResult
        """
        return self._allocation

    @allocation.setter
    def allocation(self, allocation):
        """Sets the allocation of this V1alpha3ResourceClaimStatus.


        :param allocation: The allocation of this V1alpha3ResourceClaimStatus.  # noqa: E501
        :type: V1alpha3AllocationResult
        """

        self._allocation = allocation

    @property
    def devices(self):
        """Gets the devices of this V1alpha3ResourceClaimStatus.  # noqa: E501

        Devices contains the status of each device allocated for this claim, as reported by the driver. This can include driver-specific information. Entries are owned by their respective drivers.  # noqa: E501

        :return: The devices of this V1alpha3ResourceClaimStatus.  # noqa: E501
        :rtype: list[V1alpha3AllocatedDeviceStatus]
        """
        return self._devices

    @devices.setter
    def devices(self, devices):
        """Sets the devices of this V1alpha3ResourceClaimStatus.

        Devices contains the status of each device allocated for this claim, as reported by the driver. This can include driver-specific information. Entries are owned by their respective drivers.  # noqa: E501

        :param devices: The devices of this V1alpha3ResourceClaimStatus.  # noqa: E501
        :type: list[V1alpha3AllocatedDeviceStatus]
        """

        self._devices = devices

    @property
    def reserved_for(self):
        """Gets the reserved_for of this V1alpha3ResourceClaimStatus.  # noqa: E501

        ReservedFor indicates which entities are currently allowed to use the claim. A Pod which references a ResourceClaim which is not reserved for that Pod will not be started. A claim that is in use or might be in use because it has been reserved must not get deallocated.  In a cluster with multiple scheduler instances, two pods might get scheduled concurrently by different schedulers. When they reference the same ResourceClaim which already has reached its maximum number of consumers, only one pod can be scheduled.  Both schedulers try to add their pod to the claim.status.reservedFor field, but only the update that reaches the API server first gets stored. The other one fails with an error and the scheduler which issued it knows that it must put the pod back into the queue, waiting for the ResourceClaim to become usable again.  There can be at most 256 such reservations. This may get increased in the future, but not reduced.  # noqa: E501

        :return: The reserved_for of this V1alpha3ResourceClaimStatus.  # noqa: E501
        :rtype: list[V1alpha3ResourceClaimConsumerReference]
        """
        return self._reserved_for

    @reserved_for.setter
    def reserved_for(self, reserved_for):
        """Sets the reserved_for of this V1alpha3ResourceClaimStatus.

        ReservedFor indicates which entities are currently allowed to use the claim. A Pod which references a ResourceClaim which is not reserved for that Pod will not be started. A claim that is in use or might be in use because it has been reserved must not get deallocated.  In a cluster with multiple scheduler instances, two pods might get scheduled concurrently by different schedulers. When they reference the same ResourceClaim which already has reached its maximum number of consumers, only one pod can be scheduled.  Both schedulers try to add their pod to the claim.status.reservedFor field, but only the update that reaches the API server first gets stored. The other one fails with an error and the scheduler which issued it knows that it must put the pod back into the queue, waiting for the ResourceClaim to become usable again.  There can be at most 256 such reservations. This may get increased in the future, but not reduced.  # noqa: E501

        :param reserved_for: The reserved_for of this V1alpha3ResourceClaimStatus.  # noqa: E501
        :type: list[V1alpha3ResourceClaimConsumerReference]
        """

        self._reserved_for = reserved_for

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.openapi_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, V1alpha3ResourceClaimStatus):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, V1alpha3ResourceClaimStatus):
            return True

        return self.to_dict() != other.to_dict()
