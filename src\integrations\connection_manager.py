"""
Connection Manager for WhatsApp AI Assistant.
Handles automatic reconnection and connection state management for WhatsApp Web.
"""

import asyncio
from datetime import datetime, timedelta
from typing import Dict, Any, Optional, Callable
from enum import Enum
import time

from utils.logging import get_whatsapp_logger
from utils.config import get_config

logger = get_whatsapp_logger()


class ConnectionState(Enum):
    """WhatsApp connection states."""
    DISCONNECTED = "disconnected"
    CONNECTING = "connecting"
    AUTHENTICATING = "authenticating"
    CONNECTED = "connected"
    READY = "ready"
    ERROR = "error"
    RECONNECTING = "reconnecting"


class ConnectionManager:
    """Manages WhatsApp connection state and automatic reconnection."""
    
    def __init__(self, whatsapp_client, auth_handler):
        self.whatsapp_client = whatsapp_client
        self.auth_handler = auth_handler
        self.config = get_config()
        
        # Connection state
        self.state = ConnectionState.DISCONNECTED
        self.last_state_change = datetime.now()
        self.connection_attempts = 0
        self.last_connection_attempt = None
        
        # Reconnection settings
        self.auto_reconnect = self.config.whatsapp.auto_reconnect
        self.max_reconnect_attempts = self.config.whatsapp.max_reconnect_attempts
        self.reconnect_delay = self.config.whatsapp.reconnect_delay_seconds
        self.backoff_multiplier = 2.0
        self.max_backoff_delay = 300  # 5 minutes
        
        # Health monitoring
        self.last_heartbeat = None
        self.heartbeat_interval = 30  # seconds
        self.connection_timeout = 60  # seconds
        
        # Callbacks
        self.state_change_callbacks: Dict[ConnectionState, list] = {}
        self.connection_lost_callback: Optional[Callable] = None
        self.connection_restored_callback: Optional[Callable] = None
        
        # Monitoring task
        self.monitor_task: Optional[asyncio.Task] = None
        self.running = False
        
        # Setup event handlers
        self._setup_handlers()
        
        logger.info("Connection manager initialized")
    
    def _setup_handlers(self):
        """Setup event handlers for connection events."""
        self.whatsapp_client.add_event_handler('qr_code', self._handle_qr_code)
        self.whatsapp_client.add_event_handler('authenticated', self._handle_authenticated)
        self.whatsapp_client.add_event_handler('auth_failure', self._handle_auth_failure)
        self.whatsapp_client.add_event_handler('ready', self._handle_ready)
        self.whatsapp_client.add_event_handler('disconnected', self._handle_disconnected)
        
        logger.debug("Connection event handlers registered")
    
    async def start_monitoring(self):
        """Start connection monitoring."""
        if self.running:
            logger.warning("Connection monitoring already running")
            return
        
        self.running = True
        self.monitor_task = asyncio.create_task(self._monitor_connection())
        
        logger.info("Connection monitoring started")
    
    async def stop_monitoring(self):
        """Stop connection monitoring."""
        self.running = False
        
        if self.monitor_task:
            self.monitor_task.cancel()
            try:
                await self.monitor_task
            except asyncio.CancelledError:
                pass
            self.monitor_task = None
        
        logger.info("Connection monitoring stopped")
    
    async def _monitor_connection(self):
        """Monitor connection health and handle reconnection."""
        try:
            while self.running:
                await self._check_connection_health()
                await asyncio.sleep(self.heartbeat_interval)
                
        except asyncio.CancelledError:
            logger.debug("Connection monitoring cancelled")
        except Exception as e:
            logger.error(f"Error in connection monitoring: {e}")
    
    async def _check_connection_health(self):
        """Check connection health and trigger reconnection if needed."""
        try:
            current_time = datetime.now()
            
            # Update heartbeat if client is ready
            if self.whatsapp_client.is_ready:
                self.last_heartbeat = current_time
                
                # Update state if not already ready
                if self.state != ConnectionState.READY:
                    await self._change_state(ConnectionState.READY)
                
                return
            
            # Check if we've lost connection
            if self.last_heartbeat:
                time_since_heartbeat = (current_time - self.last_heartbeat).total_seconds()
                
                if time_since_heartbeat > self.connection_timeout:
                    logger.warning(f"Connection timeout detected ({time_since_heartbeat:.1f}s)")
                    await self._handle_connection_lost()
            
            # Check if we need to attempt reconnection
            if self.state in [ConnectionState.DISCONNECTED, ConnectionState.ERROR]:
                if self.auto_reconnect and self._should_attempt_reconnect():
                    await self._attempt_reconnection()
            
        except Exception as e:
            logger.error(f"Error checking connection health: {e}")
    
    def _should_attempt_reconnect(self) -> bool:
        """Determine if we should attempt reconnection."""
        # Check if we've exceeded max attempts
        if self.connection_attempts >= self.max_reconnect_attempts:
            logger.warning(f"Max reconnection attempts ({self.max_reconnect_attempts}) reached")
            return False
        
        # Check if enough time has passed since last attempt
        if self.last_connection_attempt:
            delay = self._calculate_reconnect_delay()
            time_since_attempt = (datetime.now() - self.last_connection_attempt).total_seconds()
            
            if time_since_attempt < delay:
                return False
        
        return True
    
    def _calculate_reconnect_delay(self) -> float:
        """Calculate delay before next reconnection attempt."""
        base_delay = self.reconnect_delay
        backoff_delay = base_delay * (self.backoff_multiplier ** self.connection_attempts)
        
        return min(backoff_delay, self.max_backoff_delay)
    
    async def _attempt_reconnection(self):
        """Attempt to reconnect to WhatsApp."""
        try:
            self.connection_attempts += 1
            self.last_connection_attempt = datetime.now()
            
            logger.info(f"Attempting reconnection (attempt {self.connection_attempts}/{self.max_reconnect_attempts})")
            
            await self._change_state(ConnectionState.RECONNECTING)
            
            # Stop the current client
            if self.whatsapp_client.is_running():
                await self.whatsapp_client.stop()
                await asyncio.sleep(2)
            
            # Start the client again
            success = await self.whatsapp_client.start()
            
            if success:
                await self._change_state(ConnectionState.CONNECTING)
                logger.info("Reconnection attempt initiated")
            else:
                await self._change_state(ConnectionState.ERROR)
                logger.error("Failed to start WhatsApp client during reconnection")
            
        except Exception as e:
            logger.error(f"Error during reconnection attempt: {e}")
            await self._change_state(ConnectionState.ERROR)
    
    async def _handle_qr_code(self, qr_data: str):
        """Handle QR code event."""
        await self._change_state(ConnectionState.AUTHENTICATING)
        logger.debug("QR code received, waiting for authentication")
    
    async def _handle_authenticated(self, auth_data: Any):
        """Handle authentication success event."""
        await self._change_state(ConnectionState.CONNECTED)
        logger.info("Authentication successful, waiting for ready state")
    
    async def _handle_auth_failure(self, failure_reason: str):
        """Handle authentication failure event."""
        await self._change_state(ConnectionState.ERROR)
        logger.error(f"Authentication failed: {failure_reason}")
        
        # Reset connection attempts on auth failure
        self.connection_attempts = 0
    
    async def _handle_ready(self, ready_data: Any):
        """Handle client ready event."""
        await self._change_state(ConnectionState.READY)
        self.last_heartbeat = datetime.now()
        
        # Reset connection attempts on successful connection
        self.connection_attempts = 0
        
        logger.info("WhatsApp client is ready")
        
        # Call connection restored callback if this was a reconnection
        if self.connection_attempts > 0 and self.connection_restored_callback:
            try:
                if asyncio.iscoroutinefunction(self.connection_restored_callback):
                    await self.connection_restored_callback()
                else:
                    self.connection_restored_callback()
            except Exception as e:
                logger.error(f"Error in connection restored callback: {e}")
    
    async def _handle_disconnected(self, disconnect_reason: str):
        """Handle disconnection event."""
        await self._change_state(ConnectionState.DISCONNECTED)
        logger.warning(f"WhatsApp client disconnected: {disconnect_reason}")
        
        await self._handle_connection_lost()
    
    async def _handle_connection_lost(self):
        """Handle connection loss."""
        logger.warning("Connection lost detected")
        
        # Call connection lost callback
        if self.connection_lost_callback:
            try:
                if asyncio.iscoroutinefunction(self.connection_lost_callback):
                    await self.connection_lost_callback()
                else:
                    self.connection_lost_callback()
            except Exception as e:
                logger.error(f"Error in connection lost callback: {e}")
    
    async def _change_state(self, new_state: ConnectionState):
        """Change connection state and notify callbacks."""
        if self.state == new_state:
            return
        
        old_state = self.state
        self.state = new_state
        self.last_state_change = datetime.now()
        
        logger.debug(f"Connection state changed: {old_state.value} -> {new_state.value}")
        
        # Call state change callbacks
        if new_state in self.state_change_callbacks:
            for callback in self.state_change_callbacks[new_state]:
                try:
                    if asyncio.iscoroutinefunction(callback):
                        await callback(old_state, new_state)
                    else:
                        callback(old_state, new_state)
                except Exception as e:
                    logger.error(f"Error in state change callback: {e}")
    
    def add_state_change_callback(self, state: ConnectionState, callback: Callable):
        """Add callback for specific state changes."""
        if state not in self.state_change_callbacks:
            self.state_change_callbacks[state] = []
        
        self.state_change_callbacks[state].append(callback)
        logger.debug(f"Added state change callback for {state.value}")
    
    def set_connection_lost_callback(self, callback: Callable):
        """Set callback for connection lost events."""
        self.connection_lost_callback = callback
        logger.debug("Connection lost callback registered")
    
    def set_connection_restored_callback(self, callback: Callable):
        """Set callback for connection restored events."""
        self.connection_restored_callback = callback
        logger.debug("Connection restored callback registered")
    
    async def force_reconnect(self):
        """Force a reconnection attempt."""
        logger.info("Forcing reconnection...")
        
        # Reset connection attempts to allow reconnection
        self.connection_attempts = 0
        self.last_connection_attempt = None
        
        await self._change_state(ConnectionState.DISCONNECTED)
        await self._attempt_reconnection()
    
    async def reset_connection_attempts(self):
        """Reset connection attempt counter."""
        self.connection_attempts = 0
        self.last_connection_attempt = None
        logger.info("Connection attempts reset")
    
    def get_connection_info(self) -> Dict[str, Any]:
        """Get current connection information."""
        return {
            'state': self.state.value,
            'last_state_change': self.last_state_change.isoformat(),
            'connection_attempts': self.connection_attempts,
            'max_attempts': self.max_reconnect_attempts,
            'last_attempt': self.last_connection_attempt.isoformat() if self.last_connection_attempt else None,
            'last_heartbeat': self.last_heartbeat.isoformat() if self.last_heartbeat else None,
            'auto_reconnect': self.auto_reconnect,
            'is_monitoring': self.running
        }
    
    def get_connection_stats(self) -> Dict[str, Any]:
        """Get connection statistics."""
        current_time = datetime.now()
        
        # Calculate uptime
        uptime = None
        if self.state == ConnectionState.READY and self.last_heartbeat:
            uptime = (current_time - self.last_state_change).total_seconds()
        
        # Calculate time since last heartbeat
        time_since_heartbeat = None
        if self.last_heartbeat:
            time_since_heartbeat = (current_time - self.last_heartbeat).total_seconds()
        
        return {
            'current_state': self.state.value,
            'uptime_seconds': uptime,
            'time_since_heartbeat': time_since_heartbeat,
            'total_connection_attempts': self.connection_attempts,
            'is_healthy': self.state == ConnectionState.READY and time_since_heartbeat and time_since_heartbeat < self.connection_timeout
        }
    
    async def test_connection(self) -> bool:
        """Test the current connection."""
        try:
            if self.state != ConnectionState.READY:
                logger.warning("Connection test failed: not in ready state")
                return False
            
            # Test by getting chats (this will fail if not connected)
            await self.whatsapp_client.get_chats()
            
            # Update heartbeat
            self.last_heartbeat = datetime.now()
            
            logger.debug("Connection test successful")
            return True
            
        except Exception as e:
            logger.error(f"Connection test failed: {e}")
            await self._change_state(ConnectionState.ERROR)
            return False
