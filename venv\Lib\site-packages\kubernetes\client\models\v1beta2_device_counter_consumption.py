# coding: utf-8

"""
    Kubernetes

    No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)  # noqa: E501

    The version of the OpenAPI document: release-1.33
    Generated by: https://openapi-generator.tech
"""


import pprint
import re  # noqa: F401

import six

from kubernetes.client.configuration import Configuration


class V1beta2DeviceCounterConsumption(object):
    """NOTE: This class is auto generated by OpenAPI Generator.
    Ref: https://openapi-generator.tech

    Do not edit the class manually.
    """

    """
    Attributes:
      openapi_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    openapi_types = {
        'counter_set': 'str',
        'counters': 'dict(str, V1beta2Counter)'
    }

    attribute_map = {
        'counter_set': 'counterSet',
        'counters': 'counters'
    }

    def __init__(self, counter_set=None, counters=None, local_vars_configuration=None):  # noqa: E501
        """V1beta2DeviceCounterConsumption - a model defined in OpenAPI"""  # noqa: E501
        if local_vars_configuration is None:
            local_vars_configuration = Configuration()
        self.local_vars_configuration = local_vars_configuration

        self._counter_set = None
        self._counters = None
        self.discriminator = None

        self.counter_set = counter_set
        self.counters = counters

    @property
    def counter_set(self):
        """Gets the counter_set of this V1beta2DeviceCounterConsumption.  # noqa: E501

        CounterSet is the name of the set from which the counters defined will be consumed.  # noqa: E501

        :return: The counter_set of this V1beta2DeviceCounterConsumption.  # noqa: E501
        :rtype: str
        """
        return self._counter_set

    @counter_set.setter
    def counter_set(self, counter_set):
        """Sets the counter_set of this V1beta2DeviceCounterConsumption.

        CounterSet is the name of the set from which the counters defined will be consumed.  # noqa: E501

        :param counter_set: The counter_set of this V1beta2DeviceCounterConsumption.  # noqa: E501
        :type: str
        """
        if self.local_vars_configuration.client_side_validation and counter_set is None:  # noqa: E501
            raise ValueError("Invalid value for `counter_set`, must not be `None`")  # noqa: E501

        self._counter_set = counter_set

    @property
    def counters(self):
        """Gets the counters of this V1beta2DeviceCounterConsumption.  # noqa: E501

        Counters defines the counters that will be consumed by the device.  The maximum number counters in a device is 32. In addition, the maximum number of all counters in all devices is 1024 (for example, 64 devices with 16 counters each).  # noqa: E501

        :return: The counters of this V1beta2DeviceCounterConsumption.  # noqa: E501
        :rtype: dict(str, V1beta2Counter)
        """
        return self._counters

    @counters.setter
    def counters(self, counters):
        """Sets the counters of this V1beta2DeviceCounterConsumption.

        Counters defines the counters that will be consumed by the device.  The maximum number counters in a device is 32. In addition, the maximum number of all counters in all devices is 1024 (for example, 64 devices with 16 counters each).  # noqa: E501

        :param counters: The counters of this V1beta2DeviceCounterConsumption.  # noqa: E501
        :type: dict(str, V1beta2Counter)
        """
        if self.local_vars_configuration.client_side_validation and counters is None:  # noqa: E501
            raise ValueError("Invalid value for `counters`, must not be `None`")  # noqa: E501

        self._counters = counters

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.openapi_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, V1beta2DeviceCounterConsumption):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, V1beta2DeviceCounterConsumption):
            return True

        return self.to_dict() != other.to_dict()
