"""
Logging configuration and utilities for WhatsApp AI Assistant.
Provides structured logging with different loggers for different components.
"""

import os
import sys
import logging
import logging.handlers
from pathlib import Path
from typing import Optional, Dict, Any
from loguru import logger as loguru_logger

from .config import get_config


class ColoredFormatter(logging.Formatter):
    """Custom formatter with colors for console output."""
    
    COLORS = {
        'DEBUG': '\033[36m',     # Cyan
        'INFO': '\033[32m',      # Green
        'WARNING': '\033[33m',   # Yellow
        'ERROR': '\033[31m',     # Red
        'CRITICAL': '\033[35m',  # Magenta
        'RESET': '\033[0m'       # Reset
    }
    
    def format(self, record):
        if hasattr(record, 'levelname'):
            color = self.COLORS.get(record.levelname, self.COLORS['RESET'])
            record.levelname = f"{color}{record.levelname}{self.COLORS['RESET']}"
        return super().format(record)


class LoggerManager:
    """Manages multiple loggers for different components."""
    
    def __init__(self):
        self.loggers: Dict[str, logging.Logger] = {}
        self.config = get_config()
        self._setup_directories()
        self._setup_loggers()
    
    def _setup_directories(self):
        """Create log directories if they don't exist."""
        log_dir = Path("logs")
        log_dir.mkdir(exist_ok=True)
    
    def _setup_loggers(self):
        """Set up all component loggers."""
        # Main application logger
        self._create_logger(
            name="app",
            log_file=self.config.logging.log_file,
            level=self.config.logging.level
        )
        
        # Component-specific loggers
        components = ["whatsapp", "telegram", "ai", "scheduler", "database", "memory"]
        for component in components:
            self._create_logger(
                name=component,
                log_file=f"logs/{component}.log",
                level=self.config.logging.level
            )
    
    def _create_logger(self, name: str, log_file: str, level: str = "INFO"):
        """Create a logger with file and console handlers."""
        logger = logging.getLogger(name)
        logger.setLevel(getattr(logging, level.upper()))
        
        # Clear existing handlers
        logger.handlers.clear()
        
        # File handler with rotation
        file_handler = logging.handlers.RotatingFileHandler(
            log_file,
            maxBytes=self._parse_size(self.config.logging.max_size),
            backupCount=self.config.logging.backup_count,
            encoding='utf-8'
        )
        file_formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(funcName)s:%(lineno)d - %(message)s'
        )
        file_handler.setFormatter(file_formatter)
        logger.addHandler(file_handler)
        
        # Console handler (only for main app logger or if debug mode)
        if name == "app" or self.config.debug:
            console_handler = logging.StreamHandler(sys.stdout)
            console_formatter = ColoredFormatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            console_handler.setFormatter(console_formatter)
            logger.addHandler(console_handler)
        
        # Prevent propagation to root logger
        logger.propagate = False
        
        self.loggers[name] = logger
    
    def _parse_size(self, size_str: str) -> int:
        """Parse size string like '10MB' to bytes."""
        size_str = size_str.upper()
        if size_str.endswith('KB'):
            return int(size_str[:-2]) * 1024
        elif size_str.endswith('MB'):
            return int(size_str[:-2]) * 1024 * 1024
        elif size_str.endswith('GB'):
            return int(size_str[:-2]) * 1024 * 1024 * 1024
        else:
            return int(size_str)
    
    def get_logger(self, name: str) -> logging.Logger:
        """Get a logger by name."""
        if name not in self.loggers:
            self._create_logger(name, f"logs/{name}.log")
        return self.loggers[name]
    
    def setup_loguru(self):
        """Set up loguru logger for advanced logging features."""
        # Remove default handler
        loguru_logger.remove()
        
        # Add file handler
        loguru_logger.add(
            "logs/loguru.log",
            rotation=self.config.logging.max_size,
            retention=f"{self.config.logging.backup_count} files",
            level=self.config.logging.level,
            format="{time:YYYY-MM-DD HH:mm:ss} | {level} | {name}:{function}:{line} | {message}",
            encoding="utf-8"
        )
        
        # Add console handler if debug mode
        if self.config.debug:
            loguru_logger.add(
                sys.stdout,
                level=self.config.logging.level,
                format="<green>{time:HH:mm:ss}</green> | <level>{level}</level> | <cyan>{name}</cyan>:<cyan>{function}</cyan>:<cyan>{line}</cyan> | {message}"
            )


# Global logger manager
logger_manager = LoggerManager()


def get_logger(name: str = "app") -> logging.Logger:
    """Get a logger instance."""
    return logger_manager.get_logger(name)


def setup_logging():
    """Initialize logging system."""
    logger_manager.setup_loguru()
    
    # Log startup message
    app_logger = get_logger("app")
    config = get_config()
    app_logger.info(f"Starting {config.name} v{config.version}")
    app_logger.info(f"Debug mode: {config.debug}")
    app_logger.info(f"Log level: {config.logging.level}")


# Convenience functions for different loggers
def get_app_logger() -> logging.Logger:
    """Get the main application logger."""
    return get_logger("app")


def get_whatsapp_logger() -> logging.Logger:
    """Get the WhatsApp integration logger."""
    return get_logger("whatsapp")


def get_telegram_logger() -> logging.Logger:
    """Get the Telegram bot logger."""
    return get_logger("telegram")


def get_ai_logger() -> logging.Logger:
    """Get the AI engine logger."""
    return get_logger("ai")


def get_scheduler_logger() -> logging.Logger:
    """Get the scheduler logger."""
    return get_logger("scheduler")


def get_database_logger() -> logging.Logger:
    """Get the database logger."""
    return get_logger("database")


def get_memory_logger() -> logging.Logger:
    """Get the memory manager logger."""
    return get_logger("memory")


# Loguru logger for advanced features
def get_loguru_logger():
    """Get the loguru logger instance."""
    return loguru_logger


# Context manager for logging function calls
class LogFunctionCall:
    """Context manager to log function entry and exit."""
    
    def __init__(self, logger: logging.Logger, func_name: str, **kwargs):
        self.logger = logger
        self.func_name = func_name
        self.kwargs = kwargs
    
    def __enter__(self):
        self.logger.debug(f"Entering {self.func_name}", extra=self.kwargs)
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        if exc_type is not None:
            self.logger.error(f"Exception in {self.func_name}: {exc_val}", extra=self.kwargs)
        else:
            self.logger.debug(f"Exiting {self.func_name}", extra=self.kwargs)


def log_function_call(logger: logging.Logger, func_name: str, **kwargs):
    """Create a context manager for logging function calls."""
    return LogFunctionCall(logger, func_name, **kwargs)
