"""
Message Sender for WhatsApp AI Assistant.
Handles sending messages through WhatsApp with proper formatting and delivery tracking.
"""

import asyncio
from datetime import datetime
from typing import Dict, Any, Optional, List
import re
import time

from utils.logging import get_whatsapp_logger
from utils.config import get_config
from data.database import get_database_manager
from data.models import Contact, Message, AIResponse

logger = get_whatsapp_logger()


class MessageSender:
    """Handles sending messages through WhatsApp."""
    
    def __init__(self, whatsapp_client):
        self.whatsapp_client = whatsapp_client
        self.config = get_config()
        self.db_manager = get_database_manager()
        
        # Sending settings
        self.max_message_length = 4096  # WhatsApp message limit
        self.typing_delay = 1.0  # Seconds to wait between messages
        self.retry_attempts = 3
        self.retry_delay = 2.0
        
        # Rate limiting
        self.last_send_time = {}  # Track last send time per contact
        self.min_send_interval = 1.0  # Minimum seconds between messages to same contact
        
        logger.info("Message sender initialized")
    
    async def send_message(self, 
                          chat_id: str, 
                          message: str, 
                          contact_id: Optional[int] = None,
                          ai_response_id: Optional[int] = None,
                          options: Optional[Dict[str, Any]] = None) -> bool:
        """Send a message to a WhatsApp chat."""
        try:
            # Validate inputs
            if not chat_id or not message:
                logger.error("Chat ID and message are required")
                return False
            
            if not self.whatsapp_client.is_ready:
                logger.error("WhatsApp client is not ready")
                return False
            
            # Apply rate limiting
            if not await self._check_rate_limit(chat_id):
                logger.warning(f"Rate limit exceeded for chat {chat_id}")
                return False
            
            # Process message content
            processed_message = self._process_message_content(message)
            
            # Split long messages if necessary
            message_parts = self._split_long_message(processed_message)
            
            success = True
            for i, part in enumerate(message_parts):
                if i > 0:
                    # Add delay between message parts
                    await asyncio.sleep(self.typing_delay)
                
                part_success = await self._send_single_message(
                    chat_id, part, contact_id, ai_response_id, options
                )
                
                if not part_success:
                    success = False
                    break
            
            # Update last send time
            self.last_send_time[chat_id] = time.time()
            
            return success
            
        except Exception as e:
            logger.error(f"Error sending message to {chat_id}: {e}")
            return False
    
    async def _send_single_message(self, 
                                  chat_id: str, 
                                  message: str,
                                  contact_id: Optional[int] = None,
                                  ai_response_id: Optional[int] = None,
                                  options: Optional[Dict[str, Any]] = None) -> bool:
        """Send a single message with retry logic."""
        for attempt in range(self.retry_attempts):
            try:
                # Send message through WhatsApp client
                success = await self.whatsapp_client.send_message(chat_id, message, options)
                
                if success:
                    # Store sent message in database
                    await self._store_sent_message(
                        chat_id, message, contact_id, ai_response_id
                    )
                    
                    logger.info(f"Message sent successfully to {chat_id}")
                    return True
                else:
                    logger.warning(f"Failed to send message to {chat_id} (attempt {attempt + 1})")
                    
            except Exception as e:
                logger.error(f"Error sending message (attempt {attempt + 1}): {e}")
            
            # Wait before retry
            if attempt < self.retry_attempts - 1:
                await asyncio.sleep(self.retry_delay * (attempt + 1))
        
        logger.error(f"Failed to send message to {chat_id} after {self.retry_attempts} attempts")
        return False
    
    async def _check_rate_limit(self, chat_id: str) -> bool:
        """Check if we can send a message to this chat (rate limiting)."""
        last_send = self.last_send_time.get(chat_id, 0)
        time_since_last = time.time() - last_send
        
        if time_since_last < self.min_send_interval:
            return False
        
        return True
    
    def _process_message_content(self, message: str) -> str:
        """Process message content for WhatsApp formatting."""
        # Clean up the message
        processed = message.strip()
        
        # Convert markdown-style formatting to WhatsApp formatting
        # Bold: **text** -> *text*
        processed = re.sub(r'\*\*(.*?)\*\*', r'*\1*', processed)
        
        # Italic: *text* -> _text_ (but avoid conflicts with bold)
        processed = re.sub(r'(?<!\*)\*([^*]+?)\*(?!\*)', r'_\1_', processed)
        
        # Strikethrough: ~~text~~ -> ~text~
        processed = re.sub(r'~~(.*?)~~', r'~\1~', processed)
        
        # Code: `text` -> ```text```
        processed = re.sub(r'`([^`]+?)`', r'```\1```', processed)
        
        return processed
    
    def _split_long_message(self, message: str) -> List[str]:
        """Split long messages into smaller parts."""
        if len(message) <= self.max_message_length:
            return [message]
        
        parts = []
        current_part = ""
        
        # Split by sentences first
        sentences = re.split(r'(?<=[.!?])\s+', message)
        
        for sentence in sentences:
            # If a single sentence is too long, split by words
            if len(sentence) > self.max_message_length:
                words = sentence.split()
                for word in words:
                    if len(current_part + " " + word) > self.max_message_length:
                        if current_part:
                            parts.append(current_part.strip())
                            current_part = word
                        else:
                            # Single word is too long, truncate it
                            parts.append(word[:self.max_message_length])
                    else:
                        current_part += " " + word if current_part else word
            else:
                # Check if adding this sentence would exceed the limit
                if len(current_part + " " + sentence) > self.max_message_length:
                    if current_part:
                        parts.append(current_part.strip())
                        current_part = sentence
                    else:
                        parts.append(sentence)
                else:
                    current_part += " " + sentence if current_part else sentence
        
        # Add the last part
        if current_part:
            parts.append(current_part.strip())
        
        return parts
    
    async def _store_sent_message(self, 
                                 chat_id: str, 
                                 message: str,
                                 contact_id: Optional[int] = None,
                                 ai_response_id: Optional[int] = None):
        """Store sent message in the database."""
        try:
            with self.db_manager.get_session() as session:
                # Create message record
                sent_message = Message(
                    chat_id=chat_id,
                    contact_id=contact_id,
                    message_text=message,
                    message_type="text",
                    sender_name="AI Assistant",
                    is_from_me=True,
                    timestamp=datetime.now(),
                    processed=True,
                    ai_response_generated=True if ai_response_id else False
                )
                
                session.add(sent_message)
                session.commit()
                session.refresh(sent_message)
                
                # Update AI response record if provided
                if ai_response_id:
                    ai_response = session.query(AIResponse).filter_by(id=ai_response_id).first()
                    if ai_response:
                        ai_response.final_text = message
                        ai_response.status = "sent"
                        ai_response.sent_at = datetime.now()
                        session.commit()
                
                logger.debug(f"Sent message stored: {sent_message.id}")
                
        except Exception as e:
            logger.error(f"Error storing sent message: {e}")
    
    async def send_typing_indicator(self, chat_id: str, duration: float = 2.0):
        """Send typing indicator to a chat."""
        try:
            # WhatsApp Web.js doesn't have a direct typing indicator API
            # We can simulate it by adding a delay before sending the actual message
            await asyncio.sleep(duration)
            logger.debug(f"Typing indicator simulated for {chat_id}")
            
        except Exception as e:
            logger.error(f"Error sending typing indicator: {e}")
    
    async def send_with_confirmation(self, 
                                   chat_id: str, 
                                   message: str,
                                   contact_id: Optional[int] = None,
                                   require_confirmation: bool = True) -> bool:
        """Send message with optional confirmation requirement."""
        try:
            if require_confirmation:
                # TODO: Integrate with Telegram bot for confirmation
                # For now, just log that confirmation would be required
                logger.info(f"Message requires confirmation before sending to {chat_id}")
                logger.debug(f"Message preview: {message[:100]}...")
                
                # In a real implementation, this would:
                # 1. Send message preview to Telegram bot
                # 2. Wait for user confirmation
                # 3. Send message if confirmed
                
                # For now, we'll assume confirmation is given
                confirmed = True
                
                if not confirmed:
                    logger.info("Message sending cancelled by user")
                    return False
            
            # Send the message
            return await self.send_message(chat_id, message, contact_id)
            
        except Exception as e:
            logger.error(f"Error sending message with confirmation: {e}")
            return False
    
    async def send_scheduled_message(self, 
                                   chat_id: str, 
                                   message: str,
                                   scheduled_time: datetime,
                                   contact_id: Optional[int] = None) -> bool:
        """Send a scheduled message."""
        try:
            # Calculate delay until scheduled time
            now = datetime.now()
            if scheduled_time <= now:
                # Send immediately if scheduled time has passed
                return await self.send_message(chat_id, message, contact_id)
            
            delay = (scheduled_time - now).total_seconds()
            
            logger.info(f"Message scheduled for {scheduled_time} (in {delay:.1f} seconds)")
            
            # Wait until scheduled time
            await asyncio.sleep(delay)
            
            # Send the message
            return await self.send_message(chat_id, message, contact_id)
            
        except Exception as e:
            logger.error(f"Error sending scheduled message: {e}")
            return False
    
    async def send_bulk_messages(self, 
                               messages: List[Dict[str, Any]],
                               delay_between: float = 2.0) -> Dict[str, bool]:
        """Send multiple messages with delays between them."""
        results = {}
        
        try:
            for i, msg_data in enumerate(messages):
                chat_id = msg_data.get('chat_id')
                message = msg_data.get('message')
                contact_id = msg_data.get('contact_id')
                options = msg_data.get('options')
                
                if not chat_id or not message:
                    logger.warning(f"Skipping invalid message data: {msg_data}")
                    results[chat_id or f"message_{i}"] = False
                    continue
                
                # Add delay between messages (except for the first one)
                if i > 0:
                    await asyncio.sleep(delay_between)
                
                success = await self.send_message(chat_id, message, contact_id, options=options)
                results[chat_id] = success
                
                if not success:
                    logger.warning(f"Failed to send bulk message to {chat_id}")
            
            return results
            
        except Exception as e:
            logger.error(f"Error sending bulk messages: {e}")
            return results
    
    def get_stats(self) -> Dict[str, Any]:
        """Get message sending statistics."""
        try:
            with self.db_manager.get_session() as session:
                total_sent = session.query(Message).filter_by(is_from_me=True).count()
                ai_generated = session.query(Message).filter_by(
                    is_from_me=True, 
                    ai_response_generated=True
                ).count()
                
                return {
                    'total_messages_sent': total_sent,
                    'ai_generated_messages': ai_generated,
                    'manual_messages': total_sent - ai_generated,
                    'rate_limited_chats': len(self.last_send_time)
                }
        except Exception as e:
            logger.error(f"Error getting sender stats: {e}")
            return {}
    
    async def test_connection(self, test_chat_id: Optional[str] = None) -> bool:
        """Test the message sending functionality."""
        try:
            if not self.whatsapp_client.is_ready:
                logger.error("WhatsApp client is not ready for testing")
                return False
            
            if test_chat_id:
                # Send a test message
                test_message = "🤖 WhatsApp AI Assistant test message"
                success = await self.send_message(test_chat_id, test_message)
                
                if success:
                    logger.info("Message sending test successful")
                else:
                    logger.error("Message sending test failed")
                
                return success
            else:
                logger.info("No test chat ID provided, skipping message test")
                return True
                
        except Exception as e:
            logger.error(f"Error testing message sender: {e}")
            return False
