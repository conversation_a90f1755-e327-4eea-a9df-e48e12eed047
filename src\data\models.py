"""
Database models for WhatsApp AI Assistant.
Defines SQLAlchemy models for all data entities.
"""

from datetime import datetime
from typing import Optional, Dict, Any
from sqlalchemy import (
    Column, Integer, String, Text, DateTime, Boolean, Float, 
    ForeignKey, JSON, Index, UniqueConstraint
)
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import relationship, Session
from sqlalchemy.sql import func

Base = declarative_base()


class Contact(Base):
    """Contact information and metadata."""
    __tablename__ = "contacts"
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    phone_number = Column(String(20), unique=True, nullable=False, index=True)
    name = Column(String(100), nullable=True)
    nickname = Column(String(50), nullable=True)
    category = Column(String(20), default="other", index=True)  # family, friends, work, services, other
    notes = Column(Text, nullable=True)
    last_seen = Column(DateTime, nullable=True)
    
    # Relationship analysis
    formality_level = Column(Float, default=0.5)  # 0.0 = very informal, 1.0 = very formal
    communication_frequency = Column(Float, default=0.0)  # messages per day
    response_time_avg = Column(Float, default=0.0)  # average response time in minutes
    
    # Privacy settings
    consent_given = Column(Boolean, default=False)
    privacy_level = Column(String(20), default="private")  # public, private, confidential
    
    # Metadata
    created_at = Column(DateTime, default=func.now())
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())
    
    # Relationships
    messages = relationship("Message", back_populates="contact")
    conversations = relationship("Conversation", back_populates="contact")
    scheduled_messages = relationship("ScheduledMessage", back_populates="target_contact_obj")
    
    def __repr__(self):
        return f"<Contact(phone={self.phone_number}, name={self.name})>"


class Message(Base):
    """Individual messages in conversations."""
    __tablename__ = "messages"
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    chat_id = Column(String(100), nullable=False, index=True)
    contact_id = Column(Integer, ForeignKey("contacts.id"), nullable=False, index=True)
    
    # Message content
    message_text = Column(Text, nullable=False)
    message_type = Column(String(20), default="text")  # text, image, audio, video, document
    media_path = Column(String(500), nullable=True)
    
    # Message metadata
    sender_name = Column(String(100), nullable=True)
    is_from_me = Column(Boolean, default=False, index=True)
    timestamp = Column(DateTime, nullable=False, index=True)
    replied_to_message_id = Column(Integer, ForeignKey("messages.id"), nullable=True)
    
    # AI processing
    processed = Column(Boolean, default=False, index=True)
    ai_response_generated = Column(Boolean, default=False)
    confidence_score = Column(Float, nullable=True)
    
    # Text analysis
    sentiment_score = Column(Float, nullable=True)  # -1.0 to 1.0
    emotion_tags = Column(JSON, nullable=True)  # List of detected emotions
    text_stats = Column(JSON, nullable=True)  # Length, words, emojis, etc.
    
    # Metadata
    created_at = Column(DateTime, default=func.now())
    
    # Relationships
    contact = relationship("Contact", back_populates="messages")
    replied_to = relationship("Message", remote_side=[id])
    
    # Indexes
    __table_args__ = (
        Index('idx_messages_chat_timestamp', 'chat_id', 'timestamp'),
        Index('idx_messages_contact_timestamp', 'contact_id', 'timestamp'),
    )
    
    def __repr__(self):
        return f"<Message(id={self.id}, chat_id={self.chat_id}, timestamp={self.timestamp})>"


class Conversation(Base):
    """Conversation summaries and metadata."""
    __tablename__ = "conversations"
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    contact_id = Column(Integer, ForeignKey("contacts.id"), nullable=False, index=True)
    chat_id = Column(String(100), nullable=False, index=True)
    
    # Conversation summary
    conversation_summary = Column(Text, nullable=True)
    topic_tags = Column(JSON, nullable=True)  # List of conversation topics
    last_message_timestamp = Column(DateTime, nullable=False, index=True)
    message_count = Column(Integer, default=0)
    
    # Analysis
    avg_sentiment = Column(Float, nullable=True)
    dominant_emotion = Column(String(20), nullable=True)
    formality_level = Column(Float, nullable=True)
    
    # Status
    is_active = Column(Boolean, default=True, index=True)
    needs_response = Column(Boolean, default=False, index=True)
    priority_level = Column(Integer, default=1)  # 1=low, 2=medium, 3=high
    
    # Metadata
    created_at = Column(DateTime, default=func.now())
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())
    
    # Relationships
    contact = relationship("Contact", back_populates="conversations")
    
    # Unique constraint
    __table_args__ = (
        UniqueConstraint('contact_id', 'chat_id', name='uq_conversation_contact_chat'),
    )
    
    def __repr__(self):
        return f"<Conversation(id={self.id}, contact_id={self.contact_id}, chat_id={self.chat_id})>"


class UserPattern(Base):
    """User behavioral and linguistic patterns."""
    __tablename__ = "user_patterns"
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    pattern_type = Column(String(50), nullable=False, index=True)  # linguistic, temporal, emotional
    pattern_name = Column(String(100), nullable=False)
    pattern_data = Column(JSON, nullable=False)  # Pattern-specific data
    
    # Statistics
    frequency = Column(Float, default=0.0)  # How often this pattern occurs
    confidence = Column(Float, default=0.0)  # Confidence in pattern detection
    context = Column(JSON, nullable=True)  # Context where pattern applies
    
    # Metadata
    created_at = Column(DateTime, default=func.now())
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())
    last_observed = Column(DateTime, nullable=True)
    
    # Indexes
    __table_args__ = (
        Index('idx_patterns_type_name', 'pattern_type', 'pattern_name'),
    )
    
    def __repr__(self):
        return f"<UserPattern(type={self.pattern_type}, name={self.pattern_name})>"


class ScheduledMessage(Base):
    """Scheduled messages and reminders."""
    __tablename__ = "scheduled_messages"
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    target_contact = Column(String(20), nullable=False, index=True)  # Phone number
    target_contact_id = Column(Integer, ForeignKey("contacts.id"), nullable=True)
    
    # Message content
    message_text = Column(Text, nullable=False)
    message_type = Column(String(20), default="text")
    
    # Scheduling
    scheduled_time = Column(DateTime, nullable=False, index=True)
    timezone = Column(String(50), default="Europe/Rome")
    repeat_pattern = Column(String(50), nullable=True)  # daily, weekly, monthly, etc.
    
    # Status
    status = Column(String(20), default="pending", index=True)  # pending, sent, failed, cancelled
    attempts = Column(Integer, default=0)
    last_attempt = Column(DateTime, nullable=True)
    error_message = Column(Text, nullable=True)
    
    # Metadata
    created_by = Column(String(20), default="user")  # user, system, ai
    created_at = Column(DateTime, default=func.now())
    sent_at = Column(DateTime, nullable=True)
    
    # Relationships
    target_contact_obj = relationship("Contact", back_populates="scheduled_messages")
    
    def __repr__(self):
        return f"<ScheduledMessage(id={self.id}, target={self.target_contact}, scheduled={self.scheduled_time})>"


class MemoryContext(Base):
    """Long-term memory and context storage."""
    __tablename__ = "memory_context"
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    context_type = Column(String(50), nullable=False, index=True)  # conversation, fact, preference, event
    context_key = Column(String(200), nullable=False, index=True)  # Unique identifier for the context
    
    # Content
    context_data = Column(JSON, nullable=False)  # The actual context/memory data
    summary = Column(Text, nullable=True)  # Human-readable summary
    
    # Relevance and importance
    relevance_score = Column(Float, default=0.5)  # 0.0 to 1.0
    importance_level = Column(Integer, default=1)  # 1=low, 2=medium, 3=high
    access_count = Column(Integer, default=0)  # How often this memory is accessed
    
    # Associations
    related_contacts = Column(JSON, nullable=True)  # List of contact IDs
    related_topics = Column(JSON, nullable=True)  # List of topic tags
    
    # Temporal information
    created_at = Column(DateTime, default=func.now())
    updated_at = Column(DateTime, default=func.now(), onupdate=func.now())
    last_accessed = Column(DateTime, nullable=True)
    expires_at = Column(DateTime, nullable=True)  # Optional expiration
    
    # Indexes
    __table_args__ = (
        Index('idx_memory_type_key', 'context_type', 'context_key'),
        Index('idx_memory_relevance', 'relevance_score'),
    )
    
    def __repr__(self):
        return f"<MemoryContext(type={self.context_type}, key={self.context_key})>"


class AIResponse(Base):
    """Generated AI responses and their metadata."""
    __tablename__ = "ai_responses"
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    message_id = Column(Integer, ForeignKey("messages.id"), nullable=False, index=True)
    
    # Response content
    generated_text = Column(Text, nullable=False)
    final_text = Column(Text, nullable=True)  # After user modifications
    
    # AI metadata
    model_used = Column(String(50), nullable=False)
    prompt_tokens = Column(Integer, nullable=True)
    completion_tokens = Column(Integer, nullable=True)
    confidence_score = Column(Float, nullable=True)
    
    # User feedback
    user_action = Column(String(20), nullable=True)  # sent, modified, ignored
    user_rating = Column(Integer, nullable=True)  # 1-5 rating
    modifications = Column(JSON, nullable=True)  # What the user changed
    
    # Status
    status = Column(String(20), default="generated", index=True)  # generated, approved, sent, rejected
    
    # Metadata
    created_at = Column(DateTime, default=func.now())
    sent_at = Column(DateTime, nullable=True)
    
    def __repr__(self):
        return f"<AIResponse(id={self.id}, message_id={self.message_id}, status={self.status})>"


class SystemLog(Base):
    """System events and audit log."""
    __tablename__ = "system_logs"
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    
    # Event information
    event_type = Column(String(50), nullable=False, index=True)
    event_category = Column(String(30), nullable=False, index=True)  # whatsapp, telegram, ai, system
    event_data = Column(JSON, nullable=True)
    
    # Context
    user_id = Column(String(50), nullable=True)
    contact_id = Column(Integer, nullable=True)
    message_id = Column(Integer, nullable=True)
    
    # Severity and status
    severity = Column(String(20), default="info", index=True)  # debug, info, warning, error, critical
    status = Column(String(20), default="completed")  # started, completed, failed
    
    # Message and details
    message = Column(Text, nullable=False)
    details = Column(JSON, nullable=True)
    error_trace = Column(Text, nullable=True)
    
    # Metadata
    timestamp = Column(DateTime, default=func.now(), index=True)
    
    # Indexes
    __table_args__ = (
        Index('idx_logs_category_timestamp', 'event_category', 'timestamp'),
        Index('idx_logs_severity_timestamp', 'severity', 'timestamp'),
    )
    
    def __repr__(self):
        return f"<SystemLog(type={self.event_type}, category={self.event_category}, timestamp={self.timestamp})>"
