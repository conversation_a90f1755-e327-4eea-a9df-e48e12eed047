"""
Setup script for WhatsApp AI Assistant.
"""

from setuptools import setup, find_packages
from pathlib import Path

# Read README
readme_path = Path(__file__).parent / "README.md"
long_description = readme_path.read_text(encoding="utf-8") if readme_path.exists() else ""

# Read requirements
requirements_path = Path(__file__).parent / "requirements.txt"
requirements = []
if requirements_path.exists():
    requirements = requirements_path.read_text(encoding="utf-8").strip().split("\n")
    requirements = [req.strip() for req in requirements if req.strip() and not req.startswith("#")]

setup(
    name="whatsapp-ai-assistant",
    version="1.0.0",
    description="An intelligent WhatsApp assistant with AI-powered responses and smart scheduling",
    long_description=long_description,
    long_description_content_type="text/markdown",
    author="Valerio Esposito",
    author_email="<EMAIL>",
    url="https://github.com/yourusername/whatsapp-ai-assistant",
    packages=find_packages(where="src"),
    package_dir={"": "src"},
    python_requires=">=3.8",
    install_requires=requirements,
    extras_require={
        "dev": [
            "pytest>=7.4.3",
            "pytest-asyncio>=0.21.1",
            "pytest-cov>=4.1.0",
            "black>=23.11.0",
            "flake8>=6.1.0",
            "mypy>=1.7.1",
        ],
        "voice": [
            "speechrecognition>=3.10.0",
            "pydub>=0.25.1",
        ],
        "advanced-nlp": [
            "transformers>=4.35.2",
            "torch>=2.1.1",
        ],
    },
    entry_points={
        "console_scripts": [
            "whatsapp-assistant=main:main",
        ],
    },
    classifiers=[
        "Development Status :: 4 - Beta",
        "Intended Audience :: End Users/Desktop",
        "License :: OSI Approved :: MIT License",
        "Operating System :: OS Independent",
        "Programming Language :: Python :: 3",
        "Programming Language :: Python :: 3.8",
        "Programming Language :: Python :: 3.9",
        "Programming Language :: Python :: 3.10",
        "Programming Language :: Python :: 3.11",
        "Programming Language :: Python :: 3.12",
        "Topic :: Communications :: Chat",
        "Topic :: Scientific/Engineering :: Artificial Intelligence",
    ],
    keywords="whatsapp ai assistant chatbot automation telegram",
    project_urls={
        "Bug Reports": "https://github.com/yourusername/whatsapp-ai-assistant/issues",
        "Source": "https://github.com/yourusername/whatsapp-ai-assistant",
        "Documentation": "https://github.com/yourusername/whatsapp-ai-assistant/wiki",
    },
)
