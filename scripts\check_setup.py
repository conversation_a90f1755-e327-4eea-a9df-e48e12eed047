#!/usr/bin/env python3
"""
Setup verification script for WhatsApp AI Assistant.
Checks that all components are properly configured and working.
"""

import sys
import os
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))


def check_python_version():
    """Check Python version."""
    print("🐍 Checking Python version...")
    if sys.version_info >= (3, 8):
        print(f"✅ Python {sys.version.split()[0]} - OK")
        return True
    else:
        print(f"❌ Python {sys.version.split()[0]} - Requires 3.8+")
        return False


def check_dependencies():
    """Check if required Python packages are installed."""
    print("\n📦 Checking Python dependencies...")
    
    required_packages = [
        'fastapi', 'uvicorn', 'pydantic', 'openai', 'chromadb',
        'sqlalchemy', 'redis', 'apscheduler', 'aiohttp', 'python-telegram-bot',
        'cryptography', 'loguru', 'python-dotenv', 'pyyaml'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package.replace('-', '_'))
            print(f"✅ {package}")
        except ImportError:
            print(f"❌ {package} - Not installed")
            missing_packages.append(package)
    
    if missing_packages:
        print(f"\n⚠️  Missing packages: {', '.join(missing_packages)}")
        print("Run: pip install -r requirements.txt")
        return False
    
    return True


def check_configuration():
    """Check configuration files."""
    print("\n⚙️  Checking configuration...")
    
    project_root = Path(__file__).parent.parent
    
    # Check .env file
    env_file = project_root / ".env"
    if env_file.exists():
        print("✅ .env file exists")
        
        # Check for required environment variables
        with open(env_file, 'r') as f:
            env_content = f.read()
            
        required_vars = ['OPENAI_API_KEY', 'TELEGRAM_BOT_TOKEN', 'TELEGRAM_USER_ID']
        missing_vars = []
        
        for var in required_vars:
            if f"{var}=" not in env_content or f"{var}=your_" in env_content:
                missing_vars.append(var)
        
        if missing_vars:
            print(f"⚠️  Missing or placeholder values: {', '.join(missing_vars)}")
        else:
            print("✅ Required environment variables configured")
    else:
        print("❌ .env file not found")
        print("Copy .env.example to .env and configure it")
        return False
    
    # Check config.yaml
    config_file = project_root / "config" / "config.yaml"
    if config_file.exists():
        print("✅ config.yaml exists")
    else:
        print("❌ config.yaml not found")
        return False
    
    return True


def check_directories():
    """Check required directories."""
    print("\n📁 Checking directories...")
    
    project_root = Path(__file__).parent.parent
    required_dirs = ['data', 'logs', 'config', 'src', 'tests']
    
    all_exist = True
    for dir_name in required_dirs:
        dir_path = project_root / dir_name
        if dir_path.exists():
            print(f"✅ {dir_name}/")
        else:
            print(f"❌ {dir_name}/ - Missing")
            all_exist = False
    
    return all_exist


def check_database():
    """Check database connectivity."""
    print("\n🗄️  Checking database...")
    
    try:
        from utils.config import get_config
        from data.database import DatabaseManager
        
        config = get_config()
        print(f"✅ Configuration loaded")
        
        db_manager = DatabaseManager()
        if db_manager.health_check():
            print("✅ Database connection OK")
            
            # Get basic stats
            stats = db_manager.get_database_stats()
            print(f"✅ Database stats: {len(stats)} tables initialized")
            return True
        else:
            print("❌ Database connection failed")
            return False
            
    except Exception as e:
        print(f"❌ Database check failed: {e}")
        return False


def check_logging():
    """Check logging system."""
    print("\n📝 Checking logging...")
    
    try:
        from utils.logging import get_logger, setup_logging
        
        setup_logging()
        logger = get_logger("setup_check")
        logger.info("Test log message")
        
        print("✅ Logging system OK")
        return True
        
    except Exception as e:
        print(f"❌ Logging check failed: {e}")
        return False


def run_basic_test():
    """Run a basic functionality test."""
    print("\n🧪 Running basic functionality test...")
    
    try:
        # Test configuration loading
        from utils.config import get_config
        config = get_config()
        
        # Test database operations
        from data.database import DatabaseManager
        from data.models import Contact
        
        db_manager = DatabaseManager("sqlite:///:memory:")
        
        with db_manager.get_session() as session:
            # Create test contact
            contact = Contact(
                phone_number="+1234567890",
                name="Test Contact",
                category="test"
            )
            session.add(contact)
            session.commit()
            
            # Query test contact
            saved_contact = session.query(Contact).filter_by(phone_number="+1234567890").first()
            assert saved_contact is not None
            assert saved_contact.name == "Test Contact"
        
        print("✅ Basic functionality test passed")
        return True
        
    except Exception as e:
        print(f"❌ Basic functionality test failed: {e}")
        return False


def main():
    """Main check function."""
    print("🔍 WhatsApp AI Assistant - Setup Verification")
    print("=" * 50)
    
    checks = [
        ("Python Version", check_python_version),
        ("Dependencies", check_dependencies),
        ("Configuration", check_configuration),
        ("Directories", check_directories),
        ("Database", check_database),
        ("Logging", check_logging),
        ("Basic Functionality", run_basic_test),
    ]
    
    passed = 0
    total = len(checks)
    
    for check_name, check_func in checks:
        try:
            if check_func():
                passed += 1
        except Exception as e:
            print(f"❌ {check_name} check failed with error: {e}")
    
    print("\n" + "=" * 50)
    print(f"📊 Setup Verification Results: {passed}/{total} checks passed")
    
    if passed == total:
        print("🎉 All checks passed! Your setup is ready.")
        print("\n📋 Next steps:")
        print("1. Configure your API keys in .env file")
        print("2. Start the application: python src/main.py")
        print("3. Check the logs in logs/ directory")
        return True
    else:
        print("⚠️  Some checks failed. Please fix the issues above.")
        print("\n🔧 Common fixes:")
        print("- Run: pip install -r requirements.txt")
        print("- Copy .env.example to .env and configure it")
        print("- Create missing directories")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
