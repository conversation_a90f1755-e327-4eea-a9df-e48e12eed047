"""
Semantic Search Engine for WhatsApp AI Assistant.
Provides advanced semantic search capabilities across messages, conversations, and memory.
"""

import asyncio
from datetime import datetime, timedelta
from typing import Dict, Any, Optional, List, Tuple, Union
import json
import re
from collections import defaultdict

from utils.logging import get_logger
from utils.config import get_config
from data.database import get_database_manager
from data.models import Contact, Message, Conversation

from .vector_database import VectorDatabase

logger = get_logger("semantic_search")


class SemanticSearch:
    """Advanced semantic search engine for conversational AI."""
    
    def __init__(self, vector_db: VectorDatabase):
        self.config = get_config()
        self.db_manager = get_database_manager()
        self.vector_db = vector_db
        
        # Search configuration
        self.default_limit = self.config.ai.search.default_limit
        self.similarity_threshold = self.config.ai.search.similarity_threshold
        self.boost_recent_factor = self.config.ai.search.boost_recent_factor
        self.boost_important_factor = self.config.ai.search.boost_important_factor
        
        # Search result ranking weights
        self.ranking_weights = {
            'semantic_similarity': 0.4,
            'temporal_relevance': 0.2,
            'importance_score': 0.2,
            'contact_relevance': 0.1,
            'interaction_frequency': 0.1
        }
        
        # Query expansion patterns
        self.query_expansion_patterns = {
            'synonyms': {
                'happy': ['glad', 'joyful', 'pleased', 'content'],
                'sad': ['unhappy', 'depressed', 'down', 'upset'],
                'work': ['job', 'office', 'career', 'employment'],
                'family': ['relatives', 'parents', 'children', 'spouse'],
                'travel': ['trip', 'vacation', 'journey', 'visit'],
                'food': ['meal', 'dinner', 'lunch', 'restaurant', 'cooking']
            },
            'related_terms': {
                'meeting': ['conference', 'appointment', 'discussion'],
                'problem': ['issue', 'trouble', 'difficulty', 'challenge'],
                'help': ['assist', 'support', 'aid', 'guidance'],
                'money': ['cash', 'payment', 'cost', 'price', 'budget']
            }
        }
        
        # State
        self.is_initialized = False
        
        logger.info("Semantic search engine initialized")
    
    async def initialize(self) -> bool:
        """Initialize the semantic search engine."""
        try:
            if not self.vector_db.is_initialized:
                logger.error("Vector database not initialized")
                return False
            
            self.is_initialized = True
            logger.info("Semantic search engine initialized successfully")
            return True
            
        except Exception as e:
            logger.error(f"Failed to initialize semantic search engine: {e}")
            return False
    
    async def search_messages(self, 
                            query: str,
                            contact_id: Optional[int] = None,
                            chat_id: Optional[str] = None,
                            date_range: Optional[Tuple[datetime, datetime]] = None,
                            message_types: Optional[List[str]] = None,
                            limit: int = None,
                            include_context: bool = True) -> Dict[str, Any]:
        """Search messages with semantic understanding."""
        try:
            if limit is None:
                limit = self.default_limit
            
            # Expand query for better semantic matching
            expanded_query = self._expand_query(query)
            
            # Build filters
            filters = {}
            if contact_id:
                filters['contact_id'] = str(contact_id)
            if chat_id:
                filters['chat_id'] = chat_id
            if message_types:
                filters['message_type'] = message_types[0]  # ChromaDB doesn't support list filters directly
            
            # Perform vector search
            vector_results = await self.vector_db.search_similar_messages(
                query=expanded_query,
                limit=limit * 2,  # Get more results for re-ranking
                filters=filters
            )
            
            # Apply additional filtering and ranking
            filtered_results = await self._filter_and_rank_results(
                vector_results, query, contact_id, date_range, message_types
            )
            
            # Limit results
            final_results = filtered_results[:limit]
            
            # Add context if requested
            if include_context:
                for result in final_results:
                    result['context'] = await self._get_message_context(result)
            
            search_results = {
                'query': query,
                'expanded_query': expanded_query,
                'total_found': len(filtered_results),
                'returned': len(final_results),
                'results': final_results,
                'search_metadata': {
                    'contact_id': contact_id,
                    'chat_id': chat_id,
                    'date_range': [d.isoformat() if d else None for d in (date_range or [None, None])],
                    'message_types': message_types,
                    'similarity_threshold': self.similarity_threshold
                }
            }
            
            logger.debug(f"Message search completed: {len(final_results)} results for '{query}'")
            return search_results
            
        except Exception as e:
            logger.error(f"Error searching messages: {e}")
            return {'error': str(e), 'results': []}
    
    async def search_conversations(self, 
                                 query: str,
                                 contact_id: Optional[int] = None,
                                 topic_filter: Optional[List[str]] = None,
                                 sentiment_filter: Optional[str] = None,
                                 limit: int = None) -> Dict[str, Any]:
        """Search conversation summaries semantically."""
        try:
            if limit is None:
                limit = self.default_limit
            
            expanded_query = self._expand_query(query)
            
            # Build filters
            filters = {}
            if contact_id:
                filters['contact_id'] = str(contact_id)
            
            # Perform vector search
            vector_results = await self.vector_db.search_similar_conversations(
                query=expanded_query,
                limit=limit * 2,
                filters=filters
            )
            
            # Apply additional filtering
            filtered_results = []
            for result in vector_results:
                metadata = result['metadata']
                
                # Apply topic filter
                if topic_filter:
                    topics = json.loads(metadata.get('topic_tags', '[]'))
                    if not any(topic in topics for topic in topic_filter):
                        continue
                
                # Apply sentiment filter
                if sentiment_filter and metadata.get('avg_sentiment'):
                    avg_sentiment = metadata['avg_sentiment']
                    if sentiment_filter == 'positive' and avg_sentiment <= 0:
                        continue
                    elif sentiment_filter == 'negative' and avg_sentiment >= 0:
                        continue
                    elif sentiment_filter == 'neutral' and abs(avg_sentiment) > 0.2:
                        continue
                
                # Calculate enhanced score
                similarity_score = 1.0 - result['distance']
                temporal_score = self._calculate_temporal_relevance(metadata.get('last_message_timestamp'))
                
                result['enhanced_score'] = (
                    similarity_score * self.ranking_weights['semantic_similarity'] +
                    temporal_score * self.ranking_weights['temporal_relevance']
                )
                
                filtered_results.append(result)
            
            # Sort by enhanced score
            filtered_results.sort(key=lambda x: x['enhanced_score'], reverse=True)
            final_results = filtered_results[:limit]
            
            search_results = {
                'query': query,
                'expanded_query': expanded_query,
                'total_found': len(filtered_results),
                'returned': len(final_results),
                'results': final_results,
                'search_metadata': {
                    'contact_id': contact_id,
                    'topic_filter': topic_filter,
                    'sentiment_filter': sentiment_filter
                }
            }
            
            logger.debug(f"Conversation search completed: {len(final_results)} results for '{query}'")
            return search_results
            
        except Exception as e:
            logger.error(f"Error searching conversations: {e}")
            return {'error': str(e), 'results': []}
    
    async def search_memory_contexts(self, 
                                   query: str,
                                   context_types: Optional[List[str]] = None,
                                   importance_threshold: int = 1,
                                   contact_id: Optional[int] = None,
                                   limit: int = None) -> Dict[str, Any]:
        """Search memory contexts semantically."""
        try:
            if limit is None:
                limit = self.default_limit
            
            expanded_query = self._expand_query(query)
            
            # Perform vector search
            vector_results = await self.vector_db.search_memory_context(
                query=expanded_query,
                limit=limit * 2
            )
            
            # Apply filtering
            filtered_results = []
            for result in vector_results:
                metadata = result['metadata']
                
                # Apply context type filter
                if context_types and metadata.get('context_type') not in context_types:
                    continue
                
                # Apply importance threshold
                if metadata.get('importance_level', 1) < importance_threshold:
                    continue
                
                # Apply contact filter
                if contact_id:
                    related_contacts = json.loads(metadata.get('related_contacts', '[]'))
                    if str(contact_id) not in related_contacts:
                        continue
                
                # Calculate enhanced score
                similarity_score = 1.0 - result['distance']
                importance_score = metadata.get('importance_level', 1) / 3.0  # Normalize to 0-1
                relevance_score = metadata.get('relevance_score', 0.5)
                
                result['enhanced_score'] = (
                    similarity_score * self.ranking_weights['semantic_similarity'] +
                    importance_score * self.ranking_weights['importance_score'] +
                    relevance_score * 0.3
                )
                
                filtered_results.append(result)
            
            # Sort by enhanced score
            filtered_results.sort(key=lambda x: x['enhanced_score'], reverse=True)
            final_results = filtered_results[:limit]
            
            search_results = {
                'query': query,
                'expanded_query': expanded_query,
                'total_found': len(filtered_results),
                'returned': len(final_results),
                'results': final_results,
                'search_metadata': {
                    'context_types': context_types,
                    'importance_threshold': importance_threshold,
                    'contact_id': contact_id
                }
            }
            
            logger.debug(f"Memory context search completed: {len(final_results)} results for '{query}'")
            return search_results
            
        except Exception as e:
            logger.error(f"Error searching memory contexts: {e}")
            return {'error': str(e), 'results': []}
    
    async def search_across_all(self, 
                              query: str,
                              contact_id: Optional[int] = None,
                              limit_per_type: int = 5) -> Dict[str, Any]:
        """Search across all content types (messages, conversations, memory)."""
        try:
            results = {
                'query': query,
                'messages': {},
                'conversations': {},
                'memory_contexts': {},
                'combined_score': 0.0
            }
            
            # Search messages
            message_results = await self.search_messages(
                query=query,
                contact_id=contact_id,
                limit=limit_per_type,
                include_context=False
            )
            results['messages'] = message_results
            
            # Search conversations
            conversation_results = await self.search_conversations(
                query=query,
                contact_id=contact_id,
                limit=limit_per_type
            )
            results['conversations'] = conversation_results
            
            # Search memory contexts
            memory_results = await self.search_memory_contexts(
                query=query,
                contact_id=contact_id,
                limit=limit_per_type
            )
            results['memory_contexts'] = memory_results
            
            # Calculate combined relevance score
            total_results = (
                len(message_results.get('results', [])) +
                len(conversation_results.get('results', [])) +
                len(memory_results.get('results', []))
            )
            
            if total_results > 0:
                avg_scores = []
                
                if message_results.get('results'):
                    avg_scores.append(
                        sum(1.0 - r['distance'] for r in message_results['results']) / 
                        len(message_results['results'])
                    )
                
                if conversation_results.get('results'):
                    avg_scores.append(
                        sum(r.get('enhanced_score', 0) for r in conversation_results['results']) / 
                        len(conversation_results['results'])
                    )
                
                if memory_results.get('results'):
                    avg_scores.append(
                        sum(r.get('enhanced_score', 0) for r in memory_results['results']) / 
                        len(memory_results['results'])
                    )
                
                results['combined_score'] = sum(avg_scores) / len(avg_scores) if avg_scores else 0.0
            
            logger.debug(f"Cross-content search completed for '{query}' with combined score {results['combined_score']:.2f}")
            return results
            
        except Exception as e:
            logger.error(f"Error in cross-content search: {e}")
            return {'error': str(e)}
    
    def _expand_query(self, query: str) -> str:
        """Expand query with synonyms and related terms."""
        try:
            expanded_terms = [query]
            query_lower = query.lower()
            
            # Add synonyms
            for term, synonyms in self.query_expansion_patterns['synonyms'].items():
                if term in query_lower:
                    expanded_terms.extend(synonyms)
            
            # Add related terms
            for term, related in self.query_expansion_patterns['related_terms'].items():
                if term in query_lower:
                    expanded_terms.extend(related)
            
            # Remove duplicates and join
            unique_terms = list(set(expanded_terms))
            expanded_query = ' '.join(unique_terms)
            
            return expanded_query
            
        except Exception as e:
            logger.error(f"Error expanding query: {e}")
            return query
    
    async def _filter_and_rank_results(self, 
                                     vector_results: List[Dict[str, Any]],
                                     original_query: str,
                                     contact_id: Optional[int],
                                     date_range: Optional[Tuple[datetime, datetime]],
                                     message_types: Optional[List[str]]) -> List[Dict[str, Any]]:
        """Apply additional filtering and ranking to vector search results."""
        try:
            filtered_results = []
            
            for result in vector_results:
                metadata = result['metadata']
                
                # Apply date range filter
                if date_range:
                    timestamp_str = metadata.get('timestamp')
                    if timestamp_str:
                        try:
                            timestamp = datetime.fromisoformat(timestamp_str.replace('Z', '+00:00'))
                            if not (date_range[0] <= timestamp <= date_range[1]):
                                continue
                        except:
                            continue
                
                # Apply message type filter
                if message_types and metadata.get('message_type') not in message_types:
                    continue
                
                # Calculate enhanced ranking score
                similarity_score = 1.0 - result['distance']
                
                # Skip results below threshold
                if similarity_score < self.similarity_threshold:
                    continue
                
                # Calculate temporal relevance
                temporal_score = self._calculate_temporal_relevance(metadata.get('timestamp'))
                
                # Calculate contact relevance (if searching across contacts)
                contact_score = 1.0 if not contact_id or metadata.get('contact_id') == str(contact_id) else 0.5
                
                # Calculate importance score (based on message length, etc.)
                importance_score = min(1.0, metadata.get('text_length', 50) / 200.0)
                
                # Calculate interaction frequency score (placeholder)
                interaction_score = 0.5  # Would be calculated from database
                
                # Combine scores
                enhanced_score = (
                    similarity_score * self.ranking_weights['semantic_similarity'] +
                    temporal_score * self.ranking_weights['temporal_relevance'] +
                    importance_score * self.ranking_weights['importance_score'] +
                    contact_score * self.ranking_weights['contact_relevance'] +
                    interaction_score * self.ranking_weights['interaction_frequency']
                )
                
                result['enhanced_score'] = enhanced_score
                result['score_breakdown'] = {
                    'similarity': similarity_score,
                    'temporal': temporal_score,
                    'importance': importance_score,
                    'contact': contact_score,
                    'interaction': interaction_score
                }
                
                filtered_results.append(result)
            
            # Sort by enhanced score
            filtered_results.sort(key=lambda x: x['enhanced_score'], reverse=True)
            
            return filtered_results
            
        except Exception as e:
            logger.error(f"Error filtering and ranking results: {e}")
            return vector_results
    
    def _calculate_temporal_relevance(self, timestamp_str: Optional[str]) -> float:
        """Calculate temporal relevance score."""
        try:
            if not timestamp_str:
                return 0.5
            
            timestamp = datetime.fromisoformat(timestamp_str.replace('Z', '+00:00'))
            time_diff = datetime.now() - timestamp.replace(tzinfo=None)
            
            # More recent messages get higher scores
            days_old = time_diff.total_seconds() / (24 * 3600)
            
            if days_old <= 1:
                return 1.0
            elif days_old <= 7:
                return 0.8
            elif days_old <= 30:
                return 0.6
            elif days_old <= 90:
                return 0.4
            else:
                return 0.2
                
        except Exception as e:
            logger.error(f"Error calculating temporal relevance: {e}")
            return 0.5
    
    async def _get_message_context(self, result: Dict[str, Any]) -> Dict[str, Any]:
        """Get additional context for a message result."""
        try:
            metadata = result['metadata']
            message_id = metadata.get('message_id')
            
            if not message_id:
                return {}
            
            context = {}
            
            with self.db_manager.get_session() as session:
                # Get the actual message
                message = session.query(Message).filter_by(id=message_id).first()
                
                if message:
                    # Get surrounding messages for context
                    surrounding_messages = session.query(Message).filter(
                        Message.contact_id == message.contact_id,
                        Message.timestamp.between(
                            message.timestamp - timedelta(minutes=30),
                            message.timestamp + timedelta(minutes=30)
                        )
                    ).order_by(Message.timestamp).all()
                    
                    context['surrounding_messages'] = [
                        {
                            'text': msg.message_text,
                            'timestamp': msg.timestamp.isoformat(),
                            'is_from_me': msg.is_from_me,
                            'is_target': msg.id == message.id
                        }
                        for msg in surrounding_messages
                    ]
                    
                    # Get contact info
                    contact = session.query(Contact).filter_by(id=message.contact_id).first()
                    if contact:
                        context['contact'] = {
                            'name': contact.name,
                            'category': contact.category
                        }
            
            return context
            
        except Exception as e:
            logger.error(f"Error getting message context: {e}")
            return {}
    
    async def get_search_suggestions(self, partial_query: str, limit: int = 5) -> List[str]:
        """Get search suggestions based on partial query."""
        try:
            suggestions = []
            
            # Add query expansion suggestions
            for term, synonyms in self.query_expansion_patterns['synonyms'].items():
                if term.startswith(partial_query.lower()):
                    suggestions.append(term)
                for synonym in synonyms:
                    if synonym.startswith(partial_query.lower()):
                        suggestions.append(synonym)
            
            # Add related term suggestions
            for term, related in self.query_expansion_patterns['related_terms'].items():
                if term.startswith(partial_query.lower()):
                    suggestions.append(term)
                for related_term in related:
                    if related_term.startswith(partial_query.lower()):
                        suggestions.append(related_term)
            
            # Remove duplicates and limit
            unique_suggestions = list(set(suggestions))
            return unique_suggestions[:limit]
            
        except Exception as e:
            logger.error(f"Error getting search suggestions: {e}")
            return []
    
    def get_status(self) -> Dict[str, Any]:
        """Get semantic search engine status."""
        return {
            'initialized': self.is_initialized,
            'default_limit': self.default_limit,
            'similarity_threshold': self.similarity_threshold,
            'ranking_weights': self.ranking_weights,
            'query_expansion_enabled': bool(self.query_expansion_patterns)
        }
