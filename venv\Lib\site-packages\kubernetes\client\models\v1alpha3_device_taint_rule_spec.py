# coding: utf-8

"""
    Kubernetes

    No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)  # noqa: E501

    The version of the OpenAPI document: release-1.33
    Generated by: https://openapi-generator.tech
"""


import pprint
import re  # noqa: F401

import six

from kubernetes.client.configuration import Configuration


class V1alpha3DeviceTaintRuleSpec(object):
    """NOTE: This class is auto generated by OpenAPI Generator.
    Ref: https://openapi-generator.tech

    Do not edit the class manually.
    """

    """
    Attributes:
      openapi_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    openapi_types = {
        'device_selector': 'V1alpha3DeviceTaintSelector',
        'taint': 'V1alpha3DeviceTaint'
    }

    attribute_map = {
        'device_selector': 'deviceSelector',
        'taint': 'taint'
    }

    def __init__(self, device_selector=None, taint=None, local_vars_configuration=None):  # noqa: E501
        """V1alpha3DeviceTaintRuleSpec - a model defined in OpenAPI"""  # noqa: E501
        if local_vars_configuration is None:
            local_vars_configuration = Configuration()
        self.local_vars_configuration = local_vars_configuration

        self._device_selector = None
        self._taint = None
        self.discriminator = None

        if device_selector is not None:
            self.device_selector = device_selector
        self.taint = taint

    @property
    def device_selector(self):
        """Gets the device_selector of this V1alpha3DeviceTaintRuleSpec.  # noqa: E501


        :return: The device_selector of this V1alpha3DeviceTaintRuleSpec.  # noqa: E501
        :rtype: V1alpha3DeviceTaintSelector
        """
        return self._device_selector

    @device_selector.setter
    def device_selector(self, device_selector):
        """Sets the device_selector of this V1alpha3DeviceTaintRuleSpec.


        :param device_selector: The device_selector of this V1alpha3DeviceTaintRuleSpec.  # noqa: E501
        :type: V1alpha3DeviceTaintSelector
        """

        self._device_selector = device_selector

    @property
    def taint(self):
        """Gets the taint of this V1alpha3DeviceTaintRuleSpec.  # noqa: E501


        :return: The taint of this V1alpha3DeviceTaintRuleSpec.  # noqa: E501
        :rtype: V1alpha3DeviceTaint
        """
        return self._taint

    @taint.setter
    def taint(self, taint):
        """Sets the taint of this V1alpha3DeviceTaintRuleSpec.


        :param taint: The taint of this V1alpha3DeviceTaintRuleSpec.  # noqa: E501
        :type: V1alpha3DeviceTaint
        """
        if self.local_vars_configuration.client_side_validation and taint is None:  # noqa: E501
            raise ValueError("Invalid value for `taint`, must not be `None`")  # noqa: E501

        self._taint = taint

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.openapi_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, V1alpha3DeviceTaintRuleSpec):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, V1alpha3DeviceTaintRuleSpec):
            return True

        return self.to_dict() != other.to_dict()
