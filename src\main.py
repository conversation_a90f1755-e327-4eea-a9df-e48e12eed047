"""
Main entry point for WhatsApp AI Assistant.
Initializes all components and starts the application.
"""

import asyncio
import signal
import sys
from pathlib import Path

# Add src to Python path
sys.path.insert(0, str(Path(__file__).parent))

from utils.config import get_config
from utils.logging import setup_logging, get_app_logger
from data.database import init_database


class WhatsAppAssistant:
    """Main application class."""
    
    def __init__(self):
        self.config = get_config()
        self.logger = get_app_logger()
        self.running = False
        
        # Components will be initialized later
        self.db_manager = None
        self.whatsapp_client = None
        self.telegram_bot = None
        self.ai_engine = None
        self.scheduler = None
    
    async def initialize(self):
        """Initialize all application components."""
        self.logger.info("Initializing WhatsApp AI Assistant...")
        
        try:
            # Initialize database
            self.logger.info("Initializing database...")
            self.db_manager = init_database()
            
            if not self.db_manager.health_check():
                raise Exception("Database health check failed")
            
            self.logger.info("Database initialized successfully")
            
            # TODO: Initialize other components
            # self.whatsapp_client = WhatsAppClient()
            # self.telegram_bot = TelegramBot()
            # self.ai_engine = AIEngine()
            # self.scheduler = TaskScheduler()
            
            self.logger.info("All components initialized successfully")
            
        except Exception as e:
            self.logger.error(f"Failed to initialize application: {e}")
            raise
    
    async def start(self):
        """Start the application."""
        self.logger.info("Starting WhatsApp AI Assistant...")
        
        try:
            await self.initialize()
            
            self.running = True
            self.logger.info("WhatsApp AI Assistant started successfully")
            
            # Main application loop
            while self.running:
                await asyncio.sleep(1)
                # TODO: Add main application logic here
            
        except KeyboardInterrupt:
            self.logger.info("Received shutdown signal")
        except Exception as e:
            self.logger.error(f"Application error: {e}")
            raise
        finally:
            await self.shutdown()
    
    async def shutdown(self):
        """Shutdown the application gracefully."""
        self.logger.info("Shutting down WhatsApp AI Assistant...")
        
        self.running = False
        
        # TODO: Shutdown components
        # if self.scheduler:
        #     await self.scheduler.shutdown()
        # if self.telegram_bot:
        #     await self.telegram_bot.shutdown()
        # if self.whatsapp_client:
        #     await self.whatsapp_client.shutdown()
        
        self.logger.info("WhatsApp AI Assistant shutdown complete")
    
    def handle_signal(self, signum, frame):
        """Handle shutdown signals."""
        self.logger.info(f"Received signal {signum}")
        self.running = False


async def main():
    """Main function."""
    # Setup logging
    setup_logging()
    
    # Create and start application
    app = WhatsAppAssistant()
    
    # Setup signal handlers
    signal.signal(signal.SIGINT, app.handle_signal)
    signal.signal(signal.SIGTERM, app.handle_signal)
    
    try:
        await app.start()
    except Exception as e:
        print(f"Fatal error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    # Check Python version
    if sys.version_info < (3, 8):
        print("Python 3.8 or higher is required")
        sys.exit(1)
    
    # Run the application
    asyncio.run(main())
