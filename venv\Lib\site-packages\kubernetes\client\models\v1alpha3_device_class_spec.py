# coding: utf-8

"""
    Kubernetes

    No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)  # noqa: E501

    The version of the OpenAPI document: release-1.33
    Generated by: https://openapi-generator.tech
"""


import pprint
import re  # noqa: F401

import six

from kubernetes.client.configuration import Configuration


class V1alpha3DeviceClassSpec(object):
    """NOTE: This class is auto generated by OpenAPI Generator.
    Ref: https://openapi-generator.tech

    Do not edit the class manually.
    """

    """
    Attributes:
      openapi_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    openapi_types = {
        'config': 'list[V1alpha3DeviceClassConfiguration]',
        'selectors': 'list[V1alpha3DeviceSelector]'
    }

    attribute_map = {
        'config': 'config',
        'selectors': 'selectors'
    }

    def __init__(self, config=None, selectors=None, local_vars_configuration=None):  # noqa: E501
        """V1alpha3DeviceClassSpec - a model defined in OpenAPI"""  # noqa: E501
        if local_vars_configuration is None:
            local_vars_configuration = Configuration()
        self.local_vars_configuration = local_vars_configuration

        self._config = None
        self._selectors = None
        self.discriminator = None

        if config is not None:
            self.config = config
        if selectors is not None:
            self.selectors = selectors

    @property
    def config(self):
        """Gets the config of this V1alpha3DeviceClassSpec.  # noqa: E501

        Config defines configuration parameters that apply to each device that is claimed via this class. Some classses may potentially be satisfied by multiple drivers, so each instance of a vendor configuration applies to exactly one driver.  They are passed to the driver, but are not considered while allocating the claim.  # noqa: E501

        :return: The config of this V1alpha3DeviceClassSpec.  # noqa: E501
        :rtype: list[V1alpha3DeviceClassConfiguration]
        """
        return self._config

    @config.setter
    def config(self, config):
        """Sets the config of this V1alpha3DeviceClassSpec.

        Config defines configuration parameters that apply to each device that is claimed via this class. Some classses may potentially be satisfied by multiple drivers, so each instance of a vendor configuration applies to exactly one driver.  They are passed to the driver, but are not considered while allocating the claim.  # noqa: E501

        :param config: The config of this V1alpha3DeviceClassSpec.  # noqa: E501
        :type: list[V1alpha3DeviceClassConfiguration]
        """

        self._config = config

    @property
    def selectors(self):
        """Gets the selectors of this V1alpha3DeviceClassSpec.  # noqa: E501

        Each selector must be satisfied by a device which is claimed via this class.  # noqa: E501

        :return: The selectors of this V1alpha3DeviceClassSpec.  # noqa: E501
        :rtype: list[V1alpha3DeviceSelector]
        """
        return self._selectors

    @selectors.setter
    def selectors(self, selectors):
        """Sets the selectors of this V1alpha3DeviceClassSpec.

        Each selector must be satisfied by a device which is claimed via this class.  # noqa: E501

        :param selectors: The selectors of this V1alpha3DeviceClassSpec.  # noqa: E501
        :type: list[V1alpha3DeviceSelector]
        """

        self._selectors = selectors

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.openapi_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, V1alpha3DeviceClassSpec):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, V1alpha3DeviceClassSpec):
            return True

        return self.to_dict() != other.to_dict()
