2025-06-22 23:47:32,069 - telegram - INFO - stop:124 - Stopping Telegram bot...
2025-06-22 23:47:32,214 - telegram - INFO - stop:551 - Stopping Telegram bot...
2025-06-22 23:47:32,333 - telegram.ext.Application - INFO - stop:664 - Application is stopping. This might take a moment.
2025-06-22 23:47:32,333 - telegram.ext.Application - INFO - stop:700 - Application.stop() complete
2025-06-22 23:47:32,333 - telegram - INFO - stop:559 - Telegram bot stopped
2025-06-22 23:47:32,343 - telegram - INFO - stop:137 - Telegram bot stopped
2025-06-22 23:47:53,991 - telegram - INFO - __init__:36 - Telegram manager initialized
2025-06-22 23:47:53,991 - telegram - INFO - initialize:45 - Initializing Telegram bot...
2025-06-22 23:47:53,991 - telegram - INFO - __init__:50 - Telegram bot initialized
2025-06-22 23:47:54,616 - telegram - INFO - initialize:72 - Telegram bot initialized successfully
2025-06-22 23:47:54,616 - telegram - INFO - initialize:64 - Telegram manager initialized successfully
2025-06-22 23:47:56,286 - telegram - INFO - start:91 - Starting Telegram bot...
2025-06-22 23:47:56,286 - telegram - INFO - start:614 - Starting Telegram bot...
2025-06-22 23:48:00,647 - telegram.ext.Application - INFO - start:630 - Application started
2025-06-22 23:48:00,688 - telegram - INFO - start:624 - Telegram bot started successfully
2025-06-22 23:48:00,696 - telegram - INFO - start:101 - Telegram bot started successfully
2025-06-22 23:48:21,661 - telegram - INFO - _handle_start:156 - Bot started by user 128335946
2025-06-22 23:52:30,448 - telegram - INFO - stop:124 - Stopping Telegram bot...
2025-06-22 23:52:30,692 - telegram - INFO - stop:637 - Stopping Telegram bot...
2025-06-22 23:52:30,812 - telegram.ext.Application - INFO - stop:664 - Application is stopping. This might take a moment.
2025-06-22 23:52:30,812 - telegram.ext.Application - INFO - stop:700 - Application.stop() complete
2025-06-22 23:52:30,812 - telegram - INFO - stop:645 - Telegram bot stopped
2025-06-22 23:52:30,820 - telegram - INFO - stop:137 - Telegram bot stopped
2025-06-22 23:53:16,851 - telegram - INFO - __init__:36 - Telegram manager initialized
2025-06-22 23:53:16,851 - telegram - INFO - initialize:45 - Initializing Telegram bot...
2025-06-22 23:53:16,851 - telegram - INFO - __init__:50 - Telegram bot initialized
2025-06-22 23:53:17,488 - telegram - INFO - initialize:72 - Telegram bot initialized successfully
2025-06-22 23:53:17,498 - telegram - INFO - initialize:64 - Telegram manager initialized successfully
2025-06-22 23:53:19,063 - telegram - INFO - start:91 - Starting Telegram bot...
2025-06-22 23:53:19,064 - telegram - INFO - start:612 - Starting Telegram bot...
2025-06-22 23:53:23,149 - telegram.ext.Application - INFO - start:630 - Application started
2025-06-22 23:53:23,189 - telegram - INFO - start:622 - Telegram bot started successfully
2025-06-22 23:53:23,189 - telegram - INFO - start:101 - Telegram bot started successfully
2025-06-22 23:54:59,563 - telegram - INFO - __init__:36 - Telegram manager initialized
2025-06-22 23:54:59,563 - telegram - INFO - initialize:45 - Initializing Telegram bot...
2025-06-22 23:54:59,563 - telegram - INFO - __init__:50 - Telegram bot initialized
2025-06-22 23:55:00,202 - telegram - INFO - initialize:72 - Telegram bot initialized successfully
2025-06-22 23:55:00,205 - telegram - INFO - initialize:64 - Telegram manager initialized successfully
2025-06-22 23:55:01,852 - telegram - INFO - start:91 - Starting Telegram bot...
2025-06-22 23:55:01,852 - telegram - INFO - start:612 - Starting Telegram bot...
2025-06-22 23:55:06,988 - telegram.ext.Application - INFO - start:630 - Application started
2025-06-22 23:55:07,032 - telegram - INFO - start:622 - Telegram bot started successfully
2025-06-22 23:55:07,037 - telegram - INFO - start:101 - Telegram bot started successfully
2025-06-22 23:55:07,158 - telegram.ext.Updater - ERROR - default_error_callback:366 - Exception happened while polling for updates.
Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\whastappAssistantV2\venv\Lib\site-packages\telegram\ext\_utils\networkloop.py", line 115, in network_retry_loop
    if not await do_action():
           ^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\whastappAssistantV2\venv\Lib\site-packages\telegram\ext\_utils\networkloop.py", line 108, in do_action
    return action_cb_task.result()
           ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\whastappAssistantV2\venv\Lib\site-packages\telegram\ext\_updater.py", line 335, in polling_action_cb
    updates = await self.bot.get_updates(
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\whastappAssistantV2\venv\Lib\site-packages\telegram\ext\_extbot.py", line 669, in get_updates
    updates = await super().get_updates(
              ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\whastappAssistantV2\venv\Lib\site-packages\telegram\_bot.py", line 4601, in get_updates
    await self._post(
  File "C:\Users\<USER>\Documents\whastappAssistantV2\venv\Lib\site-packages\telegram\_bot.py", line 697, in _post
    return await self._do_post(
           ^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\whastappAssistantV2\venv\Lib\site-packages\telegram\ext\_extbot.py", line 369, in _do_post
    return await super()._do_post(
           ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\whastappAssistantV2\venv\Lib\site-packages\telegram\_bot.py", line 726, in _do_post
    result = await request.post(
             ^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\whastappAssistantV2\venv\Lib\site-packages\telegram\request\_baserequest.py", line 197, in post
    result = await self._request_wrapper(
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\whastappAssistantV2\venv\Lib\site-packages\telegram\request\_baserequest.py", line 355, in _request_wrapper
    raise Conflict(message)
telegram.error.Conflict: Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-06-22 23:55:11,213 - telegram.ext.Updater - ERROR - default_error_callback:366 - Exception happened while polling for updates.
Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\whastappAssistantV2\venv\Lib\site-packages\telegram\ext\_utils\networkloop.py", line 115, in network_retry_loop
    if not await do_action():
           ^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\whastappAssistantV2\venv\Lib\site-packages\telegram\ext\_utils\networkloop.py", line 108, in do_action
    return action_cb_task.result()
           ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\whastappAssistantV2\venv\Lib\site-packages\telegram\ext\_updater.py", line 335, in polling_action_cb
    updates = await self.bot.get_updates(
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\whastappAssistantV2\venv\Lib\site-packages\telegram\ext\_extbot.py", line 669, in get_updates
    updates = await super().get_updates(
              ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\whastappAssistantV2\venv\Lib\site-packages\telegram\_bot.py", line 4601, in get_updates
    await self._post(
  File "C:\Users\<USER>\Documents\whastappAssistantV2\venv\Lib\site-packages\telegram\_bot.py", line 697, in _post
    return await self._do_post(
           ^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\whastappAssistantV2\venv\Lib\site-packages\telegram\ext\_extbot.py", line 369, in _do_post
    return await super()._do_post(
           ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\whastappAssistantV2\venv\Lib\site-packages\telegram\_bot.py", line 726, in _do_post
    result = await request.post(
             ^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\whastappAssistantV2\venv\Lib\site-packages\telegram\request\_baserequest.py", line 197, in post
    result = await self._request_wrapper(
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\whastappAssistantV2\venv\Lib\site-packages\telegram\request\_baserequest.py", line 355, in _request_wrapper
    raise Conflict(message)
telegram.error.Conflict: Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-06-22 23:55:12,262 - telegram.ext.Updater - ERROR - default_error_callback:366 - Exception happened while polling for updates.
Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\whastappAssistantV2\venv\Lib\site-packages\telegram\ext\_utils\networkloop.py", line 115, in network_retry_loop
    if not await do_action():
           ^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\whastappAssistantV2\venv\Lib\site-packages\telegram\ext\_utils\networkloop.py", line 108, in do_action
    return action_cb_task.result()
           ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\whastappAssistantV2\venv\Lib\site-packages\telegram\ext\_updater.py", line 335, in polling_action_cb
    updates = await self.bot.get_updates(
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\whastappAssistantV2\venv\Lib\site-packages\telegram\ext\_extbot.py", line 669, in get_updates
    updates = await super().get_updates(
              ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\whastappAssistantV2\venv\Lib\site-packages\telegram\_bot.py", line 4601, in get_updates
    await self._post(
  File "C:\Users\<USER>\Documents\whastappAssistantV2\venv\Lib\site-packages\telegram\_bot.py", line 697, in _post
    return await self._do_post(
           ^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\whastappAssistantV2\venv\Lib\site-packages\telegram\ext\_extbot.py", line 369, in _do_post
    return await super()._do_post(
           ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\whastappAssistantV2\venv\Lib\site-packages\telegram\_bot.py", line 726, in _do_post
    result = await request.post(
             ^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\whastappAssistantV2\venv\Lib\site-packages\telegram\request\_baserequest.py", line 197, in post
    result = await self._request_wrapper(
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\whastappAssistantV2\venv\Lib\site-packages\telegram\request\_baserequest.py", line 355, in _request_wrapper
    raise Conflict(message)
telegram.error.Conflict: Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-06-22 23:55:16,804 - telegram.ext.Updater - ERROR - default_error_callback:366 - Exception happened while polling for updates.
Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\whastappAssistantV2\venv\Lib\site-packages\telegram\ext\_utils\networkloop.py", line 115, in network_retry_loop
    if not await do_action():
           ^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\whastappAssistantV2\venv\Lib\site-packages\telegram\ext\_utils\networkloop.py", line 108, in do_action
    return action_cb_task.result()
           ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\whastappAssistantV2\venv\Lib\site-packages\telegram\ext\_updater.py", line 335, in polling_action_cb
    updates = await self.bot.get_updates(
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\whastappAssistantV2\venv\Lib\site-packages\telegram\ext\_extbot.py", line 669, in get_updates
    updates = await super().get_updates(
              ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\whastappAssistantV2\venv\Lib\site-packages\telegram\_bot.py", line 4601, in get_updates
    await self._post(
  File "C:\Users\<USER>\Documents\whastappAssistantV2\venv\Lib\site-packages\telegram\_bot.py", line 697, in _post
    return await self._do_post(
           ^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\whastappAssistantV2\venv\Lib\site-packages\telegram\ext\_extbot.py", line 369, in _do_post
    return await super()._do_post(
           ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\whastappAssistantV2\venv\Lib\site-packages\telegram\_bot.py", line 726, in _do_post
    result = await request.post(
             ^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\whastappAssistantV2\venv\Lib\site-packages\telegram\request\_baserequest.py", line 197, in post
    result = await self._request_wrapper(
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\whastappAssistantV2\venv\Lib\site-packages\telegram\request\_baserequest.py", line 355, in _request_wrapper
    raise Conflict(message)
telegram.error.Conflict: Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-06-22 23:55:18,334 - telegram.ext.Updater - ERROR - default_error_callback:366 - Exception happened while polling for updates.
Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\whastappAssistantV2\venv\Lib\site-packages\telegram\ext\_utils\networkloop.py", line 115, in network_retry_loop
    if not await do_action():
           ^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\whastappAssistantV2\venv\Lib\site-packages\telegram\ext\_utils\networkloop.py", line 108, in do_action
    return action_cb_task.result()
           ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\whastappAssistantV2\venv\Lib\site-packages\telegram\ext\_updater.py", line 335, in polling_action_cb
    updates = await self.bot.get_updates(
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\whastappAssistantV2\venv\Lib\site-packages\telegram\ext\_extbot.py", line 669, in get_updates
    updates = await super().get_updates(
              ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\whastappAssistantV2\venv\Lib\site-packages\telegram\_bot.py", line 4601, in get_updates
    await self._post(
  File "C:\Users\<USER>\Documents\whastappAssistantV2\venv\Lib\site-packages\telegram\_bot.py", line 697, in _post
    return await self._do_post(
           ^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\whastappAssistantV2\venv\Lib\site-packages\telegram\ext\_extbot.py", line 369, in _do_post
    return await super()._do_post(
           ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\whastappAssistantV2\venv\Lib\site-packages\telegram\_bot.py", line 726, in _do_post
    result = await request.post(
             ^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\whastappAssistantV2\venv\Lib\site-packages\telegram\request\_baserequest.py", line 197, in post
    result = await self._request_wrapper(
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\whastappAssistantV2\venv\Lib\site-packages\telegram\request\_baserequest.py", line 355, in _request_wrapper
    raise Conflict(message)
telegram.error.Conflict: Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-06-22 23:55:23,647 - telegram.ext.Updater - ERROR - default_error_callback:366 - Exception happened while polling for updates.
Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\whastappAssistantV2\venv\Lib\site-packages\telegram\ext\_utils\networkloop.py", line 115, in network_retry_loop
    if not await do_action():
           ^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\whastappAssistantV2\venv\Lib\site-packages\telegram\ext\_utils\networkloop.py", line 108, in do_action
    return action_cb_task.result()
           ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\whastappAssistantV2\venv\Lib\site-packages\telegram\ext\_updater.py", line 335, in polling_action_cb
    updates = await self.bot.get_updates(
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\whastappAssistantV2\venv\Lib\site-packages\telegram\ext\_extbot.py", line 669, in get_updates
    updates = await super().get_updates(
              ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\whastappAssistantV2\venv\Lib\site-packages\telegram\_bot.py", line 4601, in get_updates
    await self._post(
  File "C:\Users\<USER>\Documents\whastappAssistantV2\venv\Lib\site-packages\telegram\_bot.py", line 697, in _post
    return await self._do_post(
           ^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\whastappAssistantV2\venv\Lib\site-packages\telegram\ext\_extbot.py", line 369, in _do_post
    return await super()._do_post(
           ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\whastappAssistantV2\venv\Lib\site-packages\telegram\_bot.py", line 726, in _do_post
    result = await request.post(
             ^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\whastappAssistantV2\venv\Lib\site-packages\telegram\request\_baserequest.py", line 197, in post
    result = await self._request_wrapper(
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\whastappAssistantV2\venv\Lib\site-packages\telegram\request\_baserequest.py", line 355, in _request_wrapper
    raise Conflict(message)
telegram.error.Conflict: Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-06-22 23:55:25,945 - telegram.ext.Updater - ERROR - default_error_callback:366 - Exception happened while polling for updates.
Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\whastappAssistantV2\venv\Lib\site-packages\telegram\ext\_utils\networkloop.py", line 115, in network_retry_loop
    if not await do_action():
           ^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\whastappAssistantV2\venv\Lib\site-packages\telegram\ext\_utils\networkloop.py", line 108, in do_action
    return action_cb_task.result()
           ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\whastappAssistantV2\venv\Lib\site-packages\telegram\ext\_updater.py", line 335, in polling_action_cb
    updates = await self.bot.get_updates(
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\whastappAssistantV2\venv\Lib\site-packages\telegram\ext\_extbot.py", line 669, in get_updates
    updates = await super().get_updates(
              ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\whastappAssistantV2\venv\Lib\site-packages\telegram\_bot.py", line 4601, in get_updates
    await self._post(
  File "C:\Users\<USER>\Documents\whastappAssistantV2\venv\Lib\site-packages\telegram\_bot.py", line 697, in _post
    return await self._do_post(
           ^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\whastappAssistantV2\venv\Lib\site-packages\telegram\ext\_extbot.py", line 369, in _do_post
    return await super()._do_post(
           ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\whastappAssistantV2\venv\Lib\site-packages\telegram\_bot.py", line 726, in _do_post
    result = await request.post(
             ^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\whastappAssistantV2\venv\Lib\site-packages\telegram\request\_baserequest.py", line 197, in post
    result = await self._request_wrapper(
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\whastappAssistantV2\venv\Lib\site-packages\telegram\request\_baserequest.py", line 355, in _request_wrapper
    raise Conflict(message)
telegram.error.Conflict: Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-06-22 23:55:29,362 - telegram.ext.Updater - ERROR - default_error_callback:366 - Exception happened while polling for updates.
Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\whastappAssistantV2\venv\Lib\site-packages\telegram\ext\_utils\networkloop.py", line 115, in network_retry_loop
    if not await do_action():
           ^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\whastappAssistantV2\venv\Lib\site-packages\telegram\ext\_utils\networkloop.py", line 108, in do_action
    return action_cb_task.result()
           ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\whastappAssistantV2\venv\Lib\site-packages\telegram\ext\_updater.py", line 335, in polling_action_cb
    updates = await self.bot.get_updates(
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\whastappAssistantV2\venv\Lib\site-packages\telegram\ext\_extbot.py", line 669, in get_updates
    updates = await super().get_updates(
              ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\whastappAssistantV2\venv\Lib\site-packages\telegram\_bot.py", line 4601, in get_updates
    await self._post(
  File "C:\Users\<USER>\Documents\whastappAssistantV2\venv\Lib\site-packages\telegram\_bot.py", line 697, in _post
    return await self._do_post(
           ^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\whastappAssistantV2\venv\Lib\site-packages\telegram\ext\_extbot.py", line 369, in _do_post
    return await super()._do_post(
           ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\whastappAssistantV2\venv\Lib\site-packages\telegram\_bot.py", line 726, in _do_post
    result = await request.post(
             ^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\whastappAssistantV2\venv\Lib\site-packages\telegram\request\_baserequest.py", line 197, in post
    result = await self._request_wrapper(
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\whastappAssistantV2\venv\Lib\site-packages\telegram\request\_baserequest.py", line 355, in _request_wrapper
    raise Conflict(message)
telegram.error.Conflict: Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-06-22 23:55:32,786 - telegram.ext.Updater - ERROR - default_error_callback:366 - Exception happened while polling for updates.
Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\whastappAssistantV2\venv\Lib\site-packages\telegram\ext\_utils\networkloop.py", line 115, in network_retry_loop
    if not await do_action():
           ^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\whastappAssistantV2\venv\Lib\site-packages\telegram\ext\_utils\networkloop.py", line 108, in do_action
    return action_cb_task.result()
           ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\whastappAssistantV2\venv\Lib\site-packages\telegram\ext\_updater.py", line 335, in polling_action_cb
    updates = await self.bot.get_updates(
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\whastappAssistantV2\venv\Lib\site-packages\telegram\ext\_extbot.py", line 669, in get_updates
    updates = await super().get_updates(
              ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\whastappAssistantV2\venv\Lib\site-packages\telegram\_bot.py", line 4601, in get_updates
    await self._post(
  File "C:\Users\<USER>\Documents\whastappAssistantV2\venv\Lib\site-packages\telegram\_bot.py", line 697, in _post
    return await self._do_post(
           ^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\whastappAssistantV2\venv\Lib\site-packages\telegram\ext\_extbot.py", line 369, in _do_post
    return await super()._do_post(
           ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\whastappAssistantV2\venv\Lib\site-packages\telegram\_bot.py", line 726, in _do_post
    result = await request.post(
             ^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\whastappAssistantV2\venv\Lib\site-packages\telegram\request\_baserequest.py", line 197, in post
    result = await self._request_wrapper(
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\whastappAssistantV2\venv\Lib\site-packages\telegram\request\_baserequest.py", line 355, in _request_wrapper
    raise Conflict(message)
telegram.error.Conflict: Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-06-22 23:55:37,985 - telegram.ext.Updater - ERROR - default_error_callback:366 - Exception happened while polling for updates.
Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\whastappAssistantV2\venv\Lib\site-packages\telegram\ext\_utils\networkloop.py", line 115, in network_retry_loop
    if not await do_action():
           ^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\whastappAssistantV2\venv\Lib\site-packages\telegram\ext\_utils\networkloop.py", line 108, in do_action
    return action_cb_task.result()
           ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\whastappAssistantV2\venv\Lib\site-packages\telegram\ext\_updater.py", line 335, in polling_action_cb
    updates = await self.bot.get_updates(
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\whastappAssistantV2\venv\Lib\site-packages\telegram\ext\_extbot.py", line 669, in get_updates
    updates = await super().get_updates(
              ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\whastappAssistantV2\venv\Lib\site-packages\telegram\_bot.py", line 4601, in get_updates
    await self._post(
  File "C:\Users\<USER>\Documents\whastappAssistantV2\venv\Lib\site-packages\telegram\_bot.py", line 697, in _post
    return await self._do_post(
           ^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\whastappAssistantV2\venv\Lib\site-packages\telegram\ext\_extbot.py", line 369, in _do_post
    return await super()._do_post(
           ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\whastappAssistantV2\venv\Lib\site-packages\telegram\_bot.py", line 726, in _do_post
    result = await request.post(
             ^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\whastappAssistantV2\venv\Lib\site-packages\telegram\request\_baserequest.py", line 197, in post
    result = await self._request_wrapper(
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\whastappAssistantV2\venv\Lib\site-packages\telegram\request\_baserequest.py", line 355, in _request_wrapper
    raise Conflict(message)
telegram.error.Conflict: Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-06-22 23:55:43,189 - telegram.ext.Updater - ERROR - default_error_callback:366 - Exception happened while polling for updates.
Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\whastappAssistantV2\venv\Lib\site-packages\telegram\ext\_utils\networkloop.py", line 115, in network_retry_loop
    if not await do_action():
           ^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\whastappAssistantV2\venv\Lib\site-packages\telegram\ext\_utils\networkloop.py", line 108, in do_action
    return action_cb_task.result()
           ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\whastappAssistantV2\venv\Lib\site-packages\telegram\ext\_updater.py", line 335, in polling_action_cb
    updates = await self.bot.get_updates(
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\whastappAssistantV2\venv\Lib\site-packages\telegram\ext\_extbot.py", line 669, in get_updates
    updates = await super().get_updates(
              ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\whastappAssistantV2\venv\Lib\site-packages\telegram\_bot.py", line 4601, in get_updates
    await self._post(
  File "C:\Users\<USER>\Documents\whastappAssistantV2\venv\Lib\site-packages\telegram\_bot.py", line 697, in _post
    return await self._do_post(
           ^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\whastappAssistantV2\venv\Lib\site-packages\telegram\ext\_extbot.py", line 369, in _do_post
    return await super()._do_post(
           ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\whastappAssistantV2\venv\Lib\site-packages\telegram\_bot.py", line 726, in _do_post
    result = await request.post(
             ^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\whastappAssistantV2\venv\Lib\site-packages\telegram\request\_baserequest.py", line 197, in post
    result = await self._request_wrapper(
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\whastappAssistantV2\venv\Lib\site-packages\telegram\request\_baserequest.py", line 355, in _request_wrapper
    raise Conflict(message)
telegram.error.Conflict: Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-06-22 23:55:44,331 - telegram - INFO - _handle_start:156 - Bot started by user 128335946
2025-06-22 23:55:50,909 - telegram.ext.Updater - ERROR - default_error_callback:366 - Exception happened while polling for updates.
Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\whastappAssistantV2\venv\Lib\site-packages\telegram\ext\_utils\networkloop.py", line 115, in network_retry_loop
    if not await do_action():
           ^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\whastappAssistantV2\venv\Lib\site-packages\telegram\ext\_utils\networkloop.py", line 108, in do_action
    return action_cb_task.result()
           ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\whastappAssistantV2\venv\Lib\site-packages\telegram\ext\_updater.py", line 335, in polling_action_cb
    updates = await self.bot.get_updates(
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\whastappAssistantV2\venv\Lib\site-packages\telegram\ext\_extbot.py", line 669, in get_updates
    updates = await super().get_updates(
              ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\whastappAssistantV2\venv\Lib\site-packages\telegram\_bot.py", line 4601, in get_updates
    await self._post(
  File "C:\Users\<USER>\Documents\whastappAssistantV2\venv\Lib\site-packages\telegram\_bot.py", line 697, in _post
    return await self._do_post(
           ^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\whastappAssistantV2\venv\Lib\site-packages\telegram\ext\_extbot.py", line 369, in _do_post
    return await super()._do_post(
           ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\whastappAssistantV2\venv\Lib\site-packages\telegram\_bot.py", line 726, in _do_post
    result = await request.post(
             ^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\whastappAssistantV2\venv\Lib\site-packages\telegram\request\_baserequest.py", line 197, in post
    result = await self._request_wrapper(
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\whastappAssistantV2\venv\Lib\site-packages\telegram\request\_baserequest.py", line 355, in _request_wrapper
    raise Conflict(message)
telegram.error.Conflict: Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-06-22 23:55:54,960 - telegram.ext.Updater - ERROR - default_error_callback:366 - Exception happened while polling for updates.
Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\whastappAssistantV2\venv\Lib\site-packages\telegram\ext\_utils\networkloop.py", line 115, in network_retry_loop
    if not await do_action():
           ^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\whastappAssistantV2\venv\Lib\site-packages\telegram\ext\_utils\networkloop.py", line 108, in do_action
    return action_cb_task.result()
           ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\whastappAssistantV2\venv\Lib\site-packages\telegram\ext\_updater.py", line 335, in polling_action_cb
    updates = await self.bot.get_updates(
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\whastappAssistantV2\venv\Lib\site-packages\telegram\ext\_extbot.py", line 669, in get_updates
    updates = await super().get_updates(
              ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\whastappAssistantV2\venv\Lib\site-packages\telegram\_bot.py", line 4601, in get_updates
    await self._post(
  File "C:\Users\<USER>\Documents\whastappAssistantV2\venv\Lib\site-packages\telegram\_bot.py", line 697, in _post
    return await self._do_post(
           ^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\whastappAssistantV2\venv\Lib\site-packages\telegram\ext\_extbot.py", line 369, in _do_post
    return await super()._do_post(
           ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\whastappAssistantV2\venv\Lib\site-packages\telegram\_bot.py", line 726, in _do_post
    result = await request.post(
             ^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\whastappAssistantV2\venv\Lib\site-packages\telegram\request\_baserequest.py", line 197, in post
    result = await self._request_wrapper(
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\whastappAssistantV2\venv\Lib\site-packages\telegram\request\_baserequest.py", line 355, in _request_wrapper
    raise Conflict(message)
telegram.error.Conflict: Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-06-22 23:56:06,484 - telegram.ext.Updater - ERROR - default_error_callback:366 - Exception happened while polling for updates.
Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\whastappAssistantV2\venv\Lib\site-packages\telegram\ext\_utils\networkloop.py", line 115, in network_retry_loop
    if not await do_action():
           ^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\whastappAssistantV2\venv\Lib\site-packages\telegram\ext\_utils\networkloop.py", line 108, in do_action
    return action_cb_task.result()
           ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\whastappAssistantV2\venv\Lib\site-packages\telegram\ext\_updater.py", line 335, in polling_action_cb
    updates = await self.bot.get_updates(
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\whastappAssistantV2\venv\Lib\site-packages\telegram\ext\_extbot.py", line 669, in get_updates
    updates = await super().get_updates(
              ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\whastappAssistantV2\venv\Lib\site-packages\telegram\_bot.py", line 4601, in get_updates
    await self._post(
  File "C:\Users\<USER>\Documents\whastappAssistantV2\venv\Lib\site-packages\telegram\_bot.py", line 697, in _post
    return await self._do_post(
           ^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\whastappAssistantV2\venv\Lib\site-packages\telegram\ext\_extbot.py", line 369, in _do_post
    return await super()._do_post(
           ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\whastappAssistantV2\venv\Lib\site-packages\telegram\_bot.py", line 726, in _do_post
    result = await request.post(
             ^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\whastappAssistantV2\venv\Lib\site-packages\telegram\request\_baserequest.py", line 197, in post
    result = await self._request_wrapper(
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\whastappAssistantV2\venv\Lib\site-packages\telegram\request\_baserequest.py", line 355, in _request_wrapper
    raise Conflict(message)
telegram.error.Conflict: Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-06-22 23:56:10,531 - telegram.ext.Updater - ERROR - default_error_callback:366 - Exception happened while polling for updates.
Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\whastappAssistantV2\venv\Lib\site-packages\telegram\ext\_utils\networkloop.py", line 115, in network_retry_loop
    if not await do_action():
           ^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\whastappAssistantV2\venv\Lib\site-packages\telegram\ext\_utils\networkloop.py", line 108, in do_action
    return action_cb_task.result()
           ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\whastappAssistantV2\venv\Lib\site-packages\telegram\ext\_updater.py", line 335, in polling_action_cb
    updates = await self.bot.get_updates(
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\whastappAssistantV2\venv\Lib\site-packages\telegram\ext\_extbot.py", line 669, in get_updates
    updates = await super().get_updates(
              ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\whastappAssistantV2\venv\Lib\site-packages\telegram\_bot.py", line 4601, in get_updates
    await self._post(
  File "C:\Users\<USER>\Documents\whastappAssistantV2\venv\Lib\site-packages\telegram\_bot.py", line 697, in _post
    return await self._do_post(
           ^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\whastappAssistantV2\venv\Lib\site-packages\telegram\ext\_extbot.py", line 369, in _do_post
    return await super()._do_post(
           ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\whastappAssistantV2\venv\Lib\site-packages\telegram\_bot.py", line 726, in _do_post
    result = await request.post(
             ^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\whastappAssistantV2\venv\Lib\site-packages\telegram\request\_baserequest.py", line 197, in post
    result = await self._request_wrapper(
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\whastappAssistantV2\venv\Lib\site-packages\telegram\request\_baserequest.py", line 355, in _request_wrapper
    raise Conflict(message)
telegram.error.Conflict: Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-06-22 23:56:27,743 - telegram.ext.Updater - ERROR - default_error_callback:366 - Exception happened while polling for updates.
Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\whastappAssistantV2\venv\Lib\site-packages\telegram\ext\_utils\networkloop.py", line 115, in network_retry_loop
    if not await do_action():
           ^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\whastappAssistantV2\venv\Lib\site-packages\telegram\ext\_utils\networkloop.py", line 108, in do_action
    return action_cb_task.result()
           ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\whastappAssistantV2\venv\Lib\site-packages\telegram\ext\_updater.py", line 335, in polling_action_cb
    updates = await self.bot.get_updates(
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\whastappAssistantV2\venv\Lib\site-packages\telegram\ext\_extbot.py", line 669, in get_updates
    updates = await super().get_updates(
              ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\whastappAssistantV2\venv\Lib\site-packages\telegram\_bot.py", line 4601, in get_updates
    await self._post(
  File "C:\Users\<USER>\Documents\whastappAssistantV2\venv\Lib\site-packages\telegram\_bot.py", line 697, in _post
    return await self._do_post(
           ^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\whastappAssistantV2\venv\Lib\site-packages\telegram\ext\_extbot.py", line 369, in _do_post
    return await super()._do_post(
           ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\whastappAssistantV2\venv\Lib\site-packages\telegram\_bot.py", line 726, in _do_post
    result = await request.post(
             ^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\whastappAssistantV2\venv\Lib\site-packages\telegram\request\_baserequest.py", line 197, in post
    result = await self._request_wrapper(
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\whastappAssistantV2\venv\Lib\site-packages\telegram\request\_baserequest.py", line 355, in _request_wrapper
    raise Conflict(message)
telegram.error.Conflict: Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-06-22 23:56:31,794 - telegram.ext.Updater - ERROR - default_error_callback:366 - Exception happened while polling for updates.
Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\whastappAssistantV2\venv\Lib\site-packages\telegram\ext\_utils\networkloop.py", line 115, in network_retry_loop
    if not await do_action():
           ^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\whastappAssistantV2\venv\Lib\site-packages\telegram\ext\_utils\networkloop.py", line 108, in do_action
    return action_cb_task.result()
           ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\whastappAssistantV2\venv\Lib\site-packages\telegram\ext\_updater.py", line 335, in polling_action_cb
    updates = await self.bot.get_updates(
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\whastappAssistantV2\venv\Lib\site-packages\telegram\ext\_extbot.py", line 669, in get_updates
    updates = await super().get_updates(
              ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\whastappAssistantV2\venv\Lib\site-packages\telegram\_bot.py", line 4601, in get_updates
    await self._post(
  File "C:\Users\<USER>\Documents\whastappAssistantV2\venv\Lib\site-packages\telegram\_bot.py", line 697, in _post
    return await self._do_post(
           ^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\whastappAssistantV2\venv\Lib\site-packages\telegram\ext\_extbot.py", line 369, in _do_post
    return await super()._do_post(
           ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\whastappAssistantV2\venv\Lib\site-packages\telegram\_bot.py", line 726, in _do_post
    result = await request.post(
             ^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\whastappAssistantV2\venv\Lib\site-packages\telegram\request\_baserequest.py", line 197, in post
    result = await self._request_wrapper(
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\whastappAssistantV2\venv\Lib\site-packages\telegram\request\_baserequest.py", line 355, in _request_wrapper
    raise Conflict(message)
telegram.error.Conflict: Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-06-22 23:56:57,540 - telegram.ext.Updater - ERROR - default_error_callback:366 - Exception happened while polling for updates.
Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\whastappAssistantV2\venv\Lib\site-packages\telegram\ext\_utils\networkloop.py", line 115, in network_retry_loop
    if not await do_action():
           ^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\whastappAssistantV2\venv\Lib\site-packages\telegram\ext\_utils\networkloop.py", line 108, in do_action
    return action_cb_task.result()
           ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\whastappAssistantV2\venv\Lib\site-packages\telegram\ext\_updater.py", line 335, in polling_action_cb
    updates = await self.bot.get_updates(
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\whastappAssistantV2\venv\Lib\site-packages\telegram\ext\_extbot.py", line 669, in get_updates
    updates = await super().get_updates(
              ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\whastappAssistantV2\venv\Lib\site-packages\telegram\_bot.py", line 4601, in get_updates
    await self._post(
  File "C:\Users\<USER>\Documents\whastappAssistantV2\venv\Lib\site-packages\telegram\_bot.py", line 697, in _post
    return await self._do_post(
           ^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\whastappAssistantV2\venv\Lib\site-packages\telegram\ext\_extbot.py", line 369, in _do_post
    return await super()._do_post(
           ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\whastappAssistantV2\venv\Lib\site-packages\telegram\_bot.py", line 726, in _do_post
    result = await request.post(
             ^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\whastappAssistantV2\venv\Lib\site-packages\telegram\request\_baserequest.py", line 197, in post
    result = await self._request_wrapper(
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\whastappAssistantV2\venv\Lib\site-packages\telegram\request\_baserequest.py", line 355, in _request_wrapper
    raise Conflict(message)
telegram.error.Conflict: Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
2025-06-22 23:57:01,580 - telegram.ext.Updater - ERROR - default_error_callback:366 - Exception happened while polling for updates.
Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\whastappAssistantV2\venv\Lib\site-packages\telegram\ext\_utils\networkloop.py", line 115, in network_retry_loop
    if not await do_action():
           ^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\whastappAssistantV2\venv\Lib\site-packages\telegram\ext\_utils\networkloop.py", line 108, in do_action
    return action_cb_task.result()
           ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\whastappAssistantV2\venv\Lib\site-packages\telegram\ext\_updater.py", line 335, in polling_action_cb
    updates = await self.bot.get_updates(
              ^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\whastappAssistantV2\venv\Lib\site-packages\telegram\ext\_extbot.py", line 669, in get_updates
    updates = await super().get_updates(
              ^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\whastappAssistantV2\venv\Lib\site-packages\telegram\_bot.py", line 4601, in get_updates
    await self._post(
  File "C:\Users\<USER>\Documents\whastappAssistantV2\venv\Lib\site-packages\telegram\_bot.py", line 697, in _post
    return await self._do_post(
           ^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\whastappAssistantV2\venv\Lib\site-packages\telegram\ext\_extbot.py", line 369, in _do_post
    return await super()._do_post(
           ^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\whastappAssistantV2\venv\Lib\site-packages\telegram\_bot.py", line 726, in _do_post
    result = await request.post(
             ^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\whastappAssistantV2\venv\Lib\site-packages\telegram\request\_baserequest.py", line 197, in post
    result = await self._request_wrapper(
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\whastappAssistantV2\venv\Lib\site-packages\telegram\request\_baserequest.py", line 355, in _request_wrapper
    raise Conflict(message)
telegram.error.Conflict: Conflict: terminated by other getUpdates request; make sure that only one bot instance is running
