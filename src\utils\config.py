"""
Configuration management for WhatsApp AI Assistant.
Handles loading and validation of configuration from environment variables and YAML files.
"""

import os
import yaml
from pathlib import Path
from typing import Dict, Any, Optional
from pydantic import BaseModel, Field, validator
from dotenv import load_dotenv


class OpenAIConfig(BaseModel):
    """OpenAI configuration settings."""
    api_key: str = ""
    model: str = "gpt-3.5-turbo"
    embedding_model: str = "text-embedding-ada-002"
    max_tokens: int = 1000
    temperature: float = 0.7
    timeout_seconds: int = 30


class ResponseConfig(BaseModel):
    """Response generation configuration."""
    max_length: int = 1000
    min_confidence_threshold: float = 0.7
    context_window_size: int = 10
    temperature: float = 0.7


class MemoryConfig(BaseModel):
    """Memory management configuration."""
    short_term_hours: int = 24
    long_term_days: int = 30
    context_window_messages: int = 10
    max_entries_per_contact: int = 100


class ContextConfig(BaseModel):
    """Context retrieval configuration."""
    max_context_length: int = 4000
    similarity_threshold: float = 0.7
    relevance_weight: float = 0.8
    temporal_decay_factor: float = 0.95


class SearchConfig(BaseModel):
    """Search configuration."""
    default_limit: int = 10
    similarity_threshold: float = 0.7
    boost_recent_factor: float = 1.2
    boost_important_factor: float = 1.5


class AnalysisConfig(BaseModel):
    """Analysis configuration."""
    window_days: int = 30
    min_messages: int = 5


class PersonalityConfig(BaseModel):
    """AI personality configuration."""
    helpfulness: str = "high"
    formality: str = "casual"
    enthusiasm: str = "moderate"
    empathy: str = "high"


class AIConfig(BaseModel):
    """AI configuration settings."""

    primary_provider: str = "openai"
    auto_process_messages: bool = True
    require_confirmation: bool = True

    # Sub-configurations
    openai: OpenAIConfig = OpenAIConfig()
    response: ResponseConfig = ResponseConfig()
    memory: MemoryConfig = MemoryConfig()
    context: ContextConfig = ContextConfig()
    search: SearchConfig = SearchConfig()
    analysis: AnalysisConfig = AnalysisConfig()
    personality: PersonalityConfig = PersonalityConfig()


class DatabaseConfig(BaseModel):
    """Database configuration settings."""

    type: str = "sqlite"
    url: str = "sqlite:///data/whatsapp_assistant.db"
    enable_fts: bool = True
    backup_interval_hours: int = 24


class WhatsAppConfig(BaseModel):
    """WhatsApp integration configuration."""

    session_path: str = "data/whatsapp_session"
    headless: bool = True
    devtools: bool = False
    auto_reconnect: bool = True
    reconnect_delay_seconds: int = 30
    max_reconnect_attempts: int = 5
    message_processing: dict = {
        'ignore_groups': False,
        'process_media': False
    }


class TelegramUIConfig(BaseModel):
    """Telegram UI configuration."""
    auto_delete_notifications: bool = True
    show_typing_indicator: bool = True
    message_preview_length: int = 100
    enable_quick_replies: bool = True


class TelegramConfig(BaseModel):
    """Telegram bot configuration."""

    bot_token: str
    user_id: str
    webhook_enabled: bool = False
    webhook_port: int = 8443
    notification_timeout_seconds: int = 300
    ui: TelegramUIConfig = TelegramUIConfig()


class ChromaDBConfig(BaseModel):
    """ChromaDB configuration."""

    persist_directory: str = "data/vector_db"


class SecurityConfig(BaseModel):
    """Security and privacy configuration."""

    enable_encryption: bool = True
    require_consent: bool = True
    data_retention_days: int = 365
    secret_key: str
    encryption_key: Optional[str] = None


class LoggingConfig(BaseModel):
    """Logging configuration."""

    level: str = "INFO"
    log_file: str = "logs/app.log"
    max_size: str = "10MB"
    backup_count: int = 5


class AppConfig(BaseModel):
    """Main application configuration."""

    name: str = "WhatsApp AI Assistant"
    version: str = "1.0.0"
    debug: bool = False
    timezone: str = "Europe/Rome"

    # Sub-configurations
    ai: AIConfig
    database: DatabaseConfig
    whatsapp: WhatsAppConfig
    telegram: TelegramConfig
    security: SecurityConfig
    logging: LoggingConfig
    chromadb: ChromaDBConfig

    @validator("telegram", pre=True)
    def validate_telegram_config(cls, v):
        """Ensure required Telegram configuration is present."""
        if isinstance(v, dict):
            if not v.get("bot_token"):
                raise ValueError("Telegram bot token is required")
            if not v.get("user_id"):
                raise ValueError("Telegram user ID is required")
        return v


class ConfigManager:
    """Manages application configuration from multiple sources."""

    def __init__(
        self, config_path: Optional[str] = None, env_file: Optional[str] = None
    ):
        self.config_path = config_path or "config/config.yaml"
        self.env_file = env_file or ".env"
        self._config: Optional[AppConfig] = None

        # Load environment variables
        if os.path.exists(self.env_file):
            load_dotenv(self.env_file)

    def load_config(self) -> AppConfig:
        """Load configuration from YAML file and environment variables."""
        if self._config is not None:
            return self._config

        # Load YAML configuration
        yaml_config = {}
        if os.path.exists(self.config_path):
            with open(self.config_path, "r", encoding="utf-8") as f:
                yaml_config = yaml.safe_load(f) or {}

        # Override with environment variables
        config_dict = self._merge_env_vars(yaml_config)

        # Create and validate configuration
        self._config = AppConfig(**config_dict)
        return self._config

    def _merge_env_vars(self, yaml_config: Dict[str, Any]) -> Dict[str, Any]:
        """Merge YAML configuration with environment variables."""
        config = yaml_config.copy()

        # AI configuration
        ai_config = config.setdefault("ai", {})
        ai_config["openai_model"] = os.getenv(
            "OPENAI_MODEL", ai_config.get("openai", {}).get("model", "gpt-3.5-turbo")
        )
        ai_config["openai_max_tokens"] = int(
            os.getenv(
                "OPENAI_MAX_TOKENS", ai_config.get("openai", {}).get("max_tokens", 1000)
            )
        )
        ai_config["openai_temperature"] = float(
            os.getenv(
                "OPENAI_TEMPERATURE",
                ai_config.get("openai", {}).get("temperature", 0.7),
            )
        )
        ai_config["confidence_threshold"] = float(
            os.getenv(
                "CONFIDENCE_THRESHOLD",
                ai_config.get("response", {}).get("confidence_threshold", 0.8),
            )
        )
        ai_config["auto_send_threshold"] = float(
            os.getenv(
                "AUTO_SEND_THRESHOLD",
                ai_config.get("response", {}).get("auto_send_threshold", 0.9),
            )
        )

        # Database configuration
        db_config = config.setdefault("database", {})
        db_config["url"] = os.getenv(
            "DATABASE_URL",
            db_config.get("path", "sqlite:///data/whatsapp_assistant.db"),
        )

        # WhatsApp configuration
        wa_config = config.setdefault("whatsapp", {})
        wa_config["session_path"] = os.getenv(
            "WHATSAPP_SESSION_PATH",
            wa_config.get("session_path", "data/whatsapp_session"),
        )
        wa_config["headless"] = (
            os.getenv("WHATSAPP_HEADLESS", str(wa_config.get("headless", True))).lower()
            == "true"
        )

        # Telegram configuration
        tg_config = config.setdefault("telegram", {})
        tg_config["bot_token"] = os.getenv("TELEGRAM_BOT_TOKEN", "")
        tg_config["user_id"] = os.getenv("TELEGRAM_USER_ID", "")
        tg_config["webhook_enabled"] = (
            os.getenv("TELEGRAM_WEBHOOK_URL", "").strip() != ""
        )

        # Security configuration
        sec_config = config.setdefault("security", {})
        sec_config["secret_key"] = os.getenv("SECRET_KEY", "")
        sec_config["encryption_key"] = os.getenv("ENCRYPTION_KEY")
        sec_config["enable_encryption"] = (
            os.getenv(
                "ENABLE_ENCRYPTION", str(sec_config.get("enable_encryption", True))
            ).lower()
            == "true"
        )

        # Logging configuration
        log_config = config.setdefault("logging", {})
        log_config["level"] = os.getenv("LOG_LEVEL", log_config.get("level", "INFO"))
        log_config["log_file"] = os.getenv(
            "LOG_FILE", log_config.get("files", {}).get("app", "logs/app.log")
        )

        # App configuration
        config["debug"] = (
            os.getenv("DEBUG", str(config.get("debug", False))).lower() == "true"
        )

        return config

    def get_config(self) -> AppConfig:
        """Get the current configuration, loading it if necessary."""
        if self._config is None:
            return self.load_config()
        return self._config

    def reload_config(self) -> AppConfig:
        """Reload configuration from files."""
        self._config = None
        return self.load_config()


# Global configuration instance
config_manager = ConfigManager()


def get_config() -> AppConfig:
    """Get the global application configuration."""
    return config_manager.get_config()


def reload_config() -> AppConfig:
    """Reload the global application configuration."""
    return config_manager.reload_config()
