#!/usr/bin/env python3
"""
Setup script for WhatsApp AI Assistant.
Helps with initial configuration and environment setup.
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path
from typing import Dict, Any


class SetupManager:
    """Manages the setup process for the WhatsApp AI Assistant."""
    
    def __init__(self):
        self.project_root = Path(__file__).parent.parent
        self.venv_path = self.project_root / "venv"
        
    def check_python_version(self) -> bool:
        """Check if Python version is compatible."""
        if sys.version_info < (3, 8):
            print("❌ Python 3.8 or higher is required")
            print(f"Current version: {sys.version}")
            return False
        
        print(f"✅ Python version: {sys.version}")
        return True
    
    def check_node_version(self) -> bool:
        """Check if Node.js is available for WhatsApp integration."""
        try:
            result = subprocess.run(['node', '--version'], capture_output=True, text=True)
            if result.returncode == 0:
                print(f"✅ Node.js version: {result.stdout.strip()}")
                return True
        except FileNotFoundError:
            pass
        
        print("❌ Node.js not found. Required for WhatsApp integration.")
        print("Please install Node.js from https://nodejs.org/")
        return False
    
    def create_virtual_environment(self) -> bool:
        """Create Python virtual environment."""
        try:
            if self.venv_path.exists():
                print("✅ Virtual environment already exists")
                return True
            
            print("📦 Creating virtual environment...")
            subprocess.run([sys.executable, '-m', 'venv', str(self.venv_path)], check=True)
            print("✅ Virtual environment created")
            return True
            
        except subprocess.CalledProcessError as e:
            print(f"❌ Failed to create virtual environment: {e}")
            return False
    
    def install_python_dependencies(self) -> bool:
        """Install Python dependencies."""
        try:
            # Determine pip path
            if os.name == 'nt':  # Windows
                pip_path = self.venv_path / "Scripts" / "pip.exe"
            else:  # Unix-like
                pip_path = self.venv_path / "bin" / "pip"
            
            if not pip_path.exists():
                print("❌ pip not found in virtual environment")
                return False
            
            print("📦 Installing Python dependencies...")
            requirements_file = self.project_root / "requirements.txt"
            
            subprocess.run([
                str(pip_path), 'install', '--upgrade', 'pip'
            ], check=True)
            
            subprocess.run([
                str(pip_path), 'install', '-r', str(requirements_file)
            ], check=True)
            
            print("✅ Python dependencies installed")
            return True
            
        except subprocess.CalledProcessError as e:
            print(f"❌ Failed to install Python dependencies: {e}")
            return False
    
    def install_whatsapp_dependencies(self) -> bool:
        """Install WhatsApp Web.js dependencies."""
        try:
            print("📦 Installing WhatsApp Web.js...")
            
            # Create package.json for whatsapp-web.js
            package_json = {
                "name": "whatsapp-ai-assistant-js",
                "version": "1.0.0",
                "description": "WhatsApp integration for AI Assistant",
                "dependencies": {
                    "whatsapp-web.js": "^1.23.0",
                    "qrcode-terminal": "^0.12.0"
                }
            }
            
            import json
            js_dir = self.project_root / "js"
            js_dir.mkdir(exist_ok=True)
            
            with open(js_dir / "package.json", 'w') as f:
                json.dump(package_json, f, indent=2)
            
            # Install npm dependencies
            subprocess.run(['npm', 'install'], cwd=js_dir, check=True)
            
            print("✅ WhatsApp Web.js installed")
            return True
            
        except subprocess.CalledProcessError as e:
            print(f"❌ Failed to install WhatsApp dependencies: {e}")
            return False
        except FileNotFoundError:
            print("❌ npm not found. Please install Node.js first.")
            return False
    
    def create_config_files(self) -> bool:
        """Create configuration files from templates."""
        try:
            print("📝 Creating configuration files...")
            
            # Copy .env.example to .env if it doesn't exist
            env_example = self.project_root / ".env.example"
            env_file = self.project_root / ".env"
            
            if not env_file.exists() and env_example.exists():
                shutil.copy2(env_example, env_file)
                print("✅ Created .env file from template")
                print("⚠️  Please edit .env file with your API keys and configuration")
            else:
                print("✅ .env file already exists")
            
            # Create directories
            directories = ["data", "logs", "data/backups", "data/whatsapp_session"]
            for directory in directories:
                dir_path = self.project_root / directory
                dir_path.mkdir(parents=True, exist_ok=True)
            
            print("✅ Created necessary directories")
            return True
            
        except Exception as e:
            print(f"❌ Failed to create config files: {e}")
            return False
    
    def run_setup(self) -> bool:
        """Run the complete setup process."""
        print("🚀 Setting up WhatsApp AI Assistant...")
        print("=" * 50)
        
        steps = [
            ("Checking Python version", self.check_python_version),
            ("Checking Node.js version", self.check_node_version),
            ("Creating virtual environment", self.create_virtual_environment),
            ("Installing Python dependencies", self.install_python_dependencies),
            ("Installing WhatsApp dependencies", self.install_whatsapp_dependencies),
            ("Creating configuration files", self.create_config_files),
        ]
        
        for step_name, step_func in steps:
            print(f"\n📋 {step_name}...")
            if not step_func():
                print(f"\n❌ Setup failed at: {step_name}")
                return False
        
        print("\n" + "=" * 50)
        print("🎉 Setup completed successfully!")
        print("\n📋 Next steps:")
        print("1. Edit the .env file with your API keys")
        print("2. Configure config/config.yaml if needed")
        print("3. Activate virtual environment:")
        
        if os.name == 'nt':  # Windows
            print("   venv\\Scripts\\activate")
        else:  # Unix-like
            print("   source venv/bin/activate")
        
        print("4. Run the application:")
        print("   python src/main.py")
        
        return True


def main():
    """Main setup function."""
    setup_manager = SetupManager()
    
    if len(sys.argv) > 1 and sys.argv[1] == "--help":
        print("WhatsApp AI Assistant Setup Script")
        print("Usage: python scripts/setup.py")
        print("\nThis script will:")
        print("- Check system requirements")
        print("- Create virtual environment")
        print("- Install Python dependencies")
        print("- Install WhatsApp Web.js dependencies")
        print("- Create configuration files")
        return
    
    success = setup_manager.run_setup()
    sys.exit(0 if success else 1)


if __name__ == "__main__":
    main()
