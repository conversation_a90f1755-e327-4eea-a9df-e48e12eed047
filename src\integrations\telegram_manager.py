"""
Telegram Manager for WhatsApp AI Assistant.
Manages Telegram bot integration and advanced command functionality.
"""

import asyncio
from datetime import datetime
from typing import Dict, Any, Optional, List

from utils.logging import get_telegram_logger
from utils.config import get_config
from data.database import get_database_manager
from data.models import Contact, Message, Conversation, SystemLog

from .telegram_bot import TelegramBot

logger = get_telegram_logger()


class TelegramManager:
    """Manager for Telegram bot integration and advanced features."""
    
    def __init__(self, whatsapp_manager=None):
        self.config = get_config()
        self.db_manager = get_database_manager()
        self.whatsapp_manager = whatsapp_manager
        
        # Core components
        self.bot: Optional[TelegramBot] = None
        
        # State
        self.is_initialized = False
        self.is_running = False
        self.start_time: Optional[datetime] = None
        
        logger.info("Telegram manager initialized")
    
    async def initialize(self) -> bool:
        """Initialize the Telegram manager."""
        try:
            if self.is_initialized:
                logger.warning("Telegram manager already initialized")
                return True
            
            logger.info("Initializing Telegram bot...")
            
            # Initialize bot
            self.bot = TelegramBot(self.whatsapp_manager)
            
            # Initialize bot
            success = await self.bot.initialize()
            if not success:
                logger.error("Failed to initialize Telegram bot")
                return False
            
            # Setup callbacks
            self._setup_callbacks()
            
            self.is_initialized = True
            
            # Log initialization
            await self._log_event("telegram_initialized", "info", "Telegram manager initialized successfully")
            
            logger.info("Telegram manager initialized successfully")
            return True
            
        except Exception as e:
            logger.error(f"Failed to initialize Telegram manager: {e}")
            await self._log_event("telegram_init_failed", "error", f"Initialization failed: {e}")
            return False
    
    def _setup_callbacks(self):
        """Setup callbacks for bot events."""
        if self.bot:
            self.bot.set_message_approved_callback(self._handle_message_approved)
            self.bot.set_message_rejected_callback(self._handle_message_rejected)
        
        logger.debug("Telegram bot callbacks configured")
    
    async def start(self) -> bool:
        """Start the Telegram bot."""
        try:
            if not self.is_initialized:
                logger.error("Cannot start: Telegram manager not initialized")
                return False
            
            if self.is_running:
                logger.warning("Telegram manager already running")
                return True
            
            logger.info("Starting Telegram bot...")
            
            # Start bot
            success = await self.bot.start()
            
            if success:
                self.is_running = True
                self.start_time = datetime.now()
                
                await self._log_event("telegram_started", "info", "Telegram bot started")
                logger.info("Telegram bot started successfully")
                
                # Send startup notification
                await self.send_notification("🚀 WhatsApp AI Assistant started successfully!")
                
                return True
            else:
                logger.error("Failed to start Telegram bot")
                await self._log_event("telegram_start_failed", "error", "Failed to start Telegram bot")
                return False
            
        except Exception as e:
            logger.error(f"Error starting Telegram bot: {e}")
            await self._log_event("telegram_start_error", "error", f"Start error: {e}")
            return False
    
    async def stop(self) -> bool:
        """Stop the Telegram bot."""
        try:
            if not self.is_running:
                logger.warning("Telegram manager not running")
                return True
            
            logger.info("Stopping Telegram bot...")
            
            # Send shutdown notification
            if self.bot:
                await self.send_notification("🛑 WhatsApp AI Assistant shutting down...")
            
            # Stop bot
            if self.bot:
                await self.bot.stop()
            
            self.is_running = False
            
            await self._log_event("telegram_stopped", "info", "Telegram bot stopped")
            logger.info("Telegram bot stopped")
            return True
            
        except Exception as e:
            logger.error(f"Error stopping Telegram bot: {e}")
            await self._log_event("telegram_stop_error", "error", f"Stop error: {e}")
            return False
    
    async def restart(self) -> bool:
        """Restart the Telegram bot."""
        logger.info("Restarting Telegram bot...")
        
        success = await self.stop()
        if success:
            await asyncio.sleep(2)
            success = await self.start()
        
        if success:
            await self._log_event("telegram_restarted", "info", "Telegram bot restarted")
            logger.info("Telegram bot restarted successfully")
        else:
            await self._log_event("telegram_restart_failed", "error", "Failed to restart Telegram bot")
            logger.error("Failed to restart Telegram bot")
        
        return success
    
    async def request_message_confirmation(self, 
                                         chat_id: str, 
                                         message_text: str, 
                                         contact_name: str = "Unknown",
                                         confidence_score: Optional[float] = None,
                                         context: Optional[str] = None) -> Optional[str]:
        """Request confirmation for a message before sending."""
        if not self.is_running or not self.bot:
            logger.error("Cannot request confirmation: Telegram bot not running")
            return None
        
        return await self.bot.request_message_confirmation(
            chat_id, message_text, contact_name, confidence_score, context
        )
    
    async def send_notification(self, message: str, urgent: bool = False):
        """Send a notification to the user."""
        if not self.is_running or not self.bot:
            logger.warning("Cannot send notification: Telegram bot not running")
            return
        
        await self.bot.send_notification(message, urgent)
    
    async def notify_whatsapp_status(self, status: str, details: Optional[str] = None):
        """Notify user about WhatsApp status changes."""
        try:
            status_icons = {
                'connected': '🟢',
                'disconnected': '🔴',
                'connecting': '🟡',
                'error': '❌',
                'authenticated': '✅',
                'auth_failed': '❌'
            }
            
            icon = status_icons.get(status, 'ℹ️')
            message = f"{icon} WhatsApp Status: {status.title()}"
            
            if details:
                message += f"\n{details}"
            
            await self.send_notification(message, urgent=(status in ['disconnected', 'error', 'auth_failed']))
            
        except Exception as e:
            logger.error(f"Error sending WhatsApp status notification: {e}")
    
    async def notify_new_message(self, contact_name: str, message_preview: str, chat_id: str):
        """Notify user about new incoming messages."""
        try:
            preview = message_preview[:100] + "..." if len(message_preview) > 100 else message_preview
            
            notification = (
                f"💬 *New Message*\n\n"
                f"*From:* {contact_name}\n"
                f"*Message:* {preview}"
            )
            
            await self.send_notification(notification)
            
        except Exception as e:
            logger.error(f"Error sending new message notification: {e}")
    
    async def notify_ai_response_generated(self, contact_name: str, confidence_score: float):
        """Notify user when AI generates a response."""
        try:
            confidence_icon = "🟢" if confidence_score > 0.8 else "🟡" if confidence_score > 0.6 else "🔴"
            
            notification = (
                f"🤖 *AI Response Generated*\n\n"
                f"*For:* {contact_name}\n"
                f"*Confidence:* {confidence_icon} {confidence_score:.1%}\n\n"
                f"Check your messages for confirmation request."
            )
            
            await self.send_notification(notification)
            
        except Exception as e:
            logger.error(f"Error sending AI response notification: {e}")
    
    async def send_daily_summary(self):
        """Send daily usage summary."""
        try:
            # Get statistics for today
            stats = self.db_manager.get_database_stats()
            
            # Get WhatsApp statistics
            wa_stats = {}
            if self.whatsapp_manager:
                wa_stats = self.whatsapp_manager.get_statistics()
            
            summary = (
                f"📊 *Daily Summary - {datetime.now().strftime('%Y-%m-%d')}*\n\n"
                f"*Messages:*\n"
                f"• Received: {stats.get('total_messages', 0)}\n"
                f"• Processed: {stats.get('processed_messages', 0)}\n"
                f"• AI Responses: {stats.get('ai_responses_generated', 0)}\n\n"
                f"*Contacts:*\n"
                f"• Total: {stats.get('contacts_count', 0)}\n"
                f"• Active: {stats.get('active_contacts', 0)}\n\n"
            )
            
            if wa_stats.get('uptime_seconds'):
                uptime = wa_stats['uptime_seconds']
                hours = int(uptime // 3600)
                minutes = int((uptime % 3600) // 60)
                summary += f"*Uptime:* {hours}h {minutes}m\n"
            
            await self.send_notification(summary)
            
        except Exception as e:
            logger.error(f"Error sending daily summary: {e}")
    
    async def _handle_message_approved(self, message_id: str, confirmation_data: Dict[str, Any]):
        """Handle message approval events."""
        try:
            contact_name = confirmation_data.get('contact_name', 'Unknown')
            logger.info(f"Message approved for {contact_name}")
            
            # Log the approval
            await self._log_event(
                "message_approved", 
                "info", 
                f"Message approved for {contact_name}",
                {'message_id': message_id, 'contact_name': contact_name}
            )
            
        except Exception as e:
            logger.error(f"Error handling message approval: {e}")
    
    async def _handle_message_rejected(self, message_id: str, confirmation_data: Dict[str, Any]):
        """Handle message rejection events."""
        try:
            contact_name = confirmation_data.get('contact_name', 'Unknown')
            logger.info(f"Message rejected for {contact_name}")
            
            # Log the rejection
            await self._log_event(
                "message_rejected", 
                "info", 
                f"Message rejected for {contact_name}",
                {'message_id': message_id, 'contact_name': contact_name}
            )
            
        except Exception as e:
            logger.error(f"Error handling message rejection: {e}")
    
    async def _log_event(self, event_type: str, severity: str, message: str, details: Optional[Dict[str, Any]] = None):
        """Log Telegram events to the database."""
        try:
            with self.db_manager.get_session() as session:
                log_entry = SystemLog(
                    event_type=event_type,
                    event_category="telegram",
                    severity=severity,
                    message=message,
                    details=details,
                    timestamp=datetime.now()
                )
                session.add(log_entry)
                session.commit()
                
        except Exception as e:
            logger.error(f"Failed to log event to database: {e}")
    
    def get_status(self) -> Dict[str, Any]:
        """Get current Telegram integration status."""
        status = {
            'initialized': self.is_initialized,
            'running': self.is_running,
            'start_time': self.start_time.isoformat() if self.start_time else None
        }
        
        # Add bot-specific status
        if self.bot:
            status['bot_status'] = self.bot.get_status()
        
        return status
    
    def get_statistics(self) -> Dict[str, Any]:
        """Get Telegram integration statistics."""
        stats = {}
        
        # Bot statistics
        if self.bot:
            bot_status = self.bot.get_status()
            stats['pending_confirmations'] = bot_status.get('pending_confirmations', 0)
        
        # Uptime
        if self.start_time:
            uptime = (datetime.now() - self.start_time).total_seconds()
            stats['uptime_seconds'] = uptime
        
        return stats
    
    async def health_check(self) -> Dict[str, Any]:
        """Perform health check on Telegram integration."""
        health = {
            'healthy': True,
            'issues': [],
            'timestamp': datetime.now().isoformat()
        }
        
        try:
            # Check if initialized
            if not self.is_initialized:
                health['healthy'] = False
                health['issues'].append("Not initialized")
            
            # Check if running
            if not self.is_running:
                health['healthy'] = False
                health['issues'].append("Not running")
            
            # Check bot status
            if self.bot:
                bot_status = self.bot.get_status()
                if not bot_status.get('running'):
                    health['healthy'] = False
                    health['issues'].append("Bot not running")
            else:
                health['healthy'] = False
                health['issues'].append("Bot not initialized")
            
            return health
            
        except Exception as e:
            logger.error(f"Error during health check: {e}")
            return {
                'healthy': False,
                'issues': [f"Health check error: {e}"],
                'timestamp': datetime.now().isoformat()
            }
