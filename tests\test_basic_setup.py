"""
Basic setup tests for WhatsApp AI Assistant.
Tests that the basic configuration and database setup work correctly.
"""

import pytest
import sys
from pathlib import Path

# Add src to path for testing
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from utils.config import get_config, ConfigManager
from utils.logging import get_logger
from data.database import DatabaseManager
from data.models import Contact, Message


class TestBasicSetup:
    """Test basic application setup."""
    
    def test_config_loading(self):
        """Test that configuration loads correctly."""
        config = get_config()
        
        assert config is not None
        assert config.name == "WhatsApp AI Assistant"
        assert config.version == "1.0.0"
        assert hasattr(config, 'ai')
        assert hasattr(config, 'database')
        assert hasattr(config, 'whatsapp')
        assert hasattr(config, 'telegram')
        assert hasattr(config, 'security')
        assert hasattr(config, 'logging')
    
    def test_config_manager(self):
        """Test ConfigManager functionality."""
        config_manager = ConfigManager()
        config = config_manager.load_config()
        
        assert config is not None
        assert config.ai.primary_provider == "openai"
        assert config.database.type == "sqlite"
    
    def test_logger_creation(self):
        """Test that loggers can be created."""
        logger = get_logger("test")
        
        assert logger is not None
        assert logger.name == "test"
    
    def test_database_initialization(self):
        """Test database initialization."""
        # Use in-memory SQLite for testing
        db_manager = DatabaseManager("sqlite:///:memory:")
        
        assert db_manager is not None
        assert db_manager.health_check()
    
    def test_database_models(self):
        """Test that database models work correctly."""
        db_manager = DatabaseManager("sqlite:///:memory:")
        
        with db_manager.get_session() as session:
            # Create a test contact
            contact = Contact(
                phone_number="+**********",
                name="Test Contact",
                category="test"
            )
            session.add(contact)
            session.flush()  # Get the ID
            
            # Create a test message
            message = Message(
                chat_id="test_chat",
                contact_id=contact.id,
                message_text="Test message",
                sender_name="Test Sender",
                timestamp=pytest.importorskip("datetime").datetime.now()
            )
            session.add(message)
            session.commit()
            
            # Verify data was saved
            saved_contact = session.query(Contact).filter_by(phone_number="+**********").first()
            assert saved_contact is not None
            assert saved_contact.name == "Test Contact"
            
            saved_message = session.query(Message).filter_by(chat_id="test_chat").first()
            assert saved_message is not None
            assert saved_message.message_text == "Test message"
    
    def test_database_search(self):
        """Test database search functionality."""
        db_manager = DatabaseManager("sqlite:///:memory:")
        
        # Test that search methods don't crash
        messages = db_manager.search_messages("test", limit=10)
        assert isinstance(messages, list)
        
        contacts = db_manager.search_contacts("test", limit=10)
        assert isinstance(contacts, list)
    
    def test_database_stats(self):
        """Test database statistics."""
        db_manager = DatabaseManager("sqlite:///:memory:")
        
        stats = db_manager.get_database_stats()
        assert isinstance(stats, dict)
        assert 'contacts_count' in stats
        assert 'messages_count' in stats


class TestEnvironmentSetup:
    """Test environment and dependency setup."""
    
    def test_python_version(self):
        """Test that Python version is compatible."""
        assert sys.version_info >= (3, 8), "Python 3.8 or higher is required"
    
    def test_required_directories(self):
        """Test that required directories exist or can be created."""
        project_root = Path(__file__).parent.parent
        
        required_dirs = [
            "src",
            "config",
            "tests"
        ]
        
        for dir_name in required_dirs:
            dir_path = project_root / dir_name
            assert dir_path.exists(), f"Required directory {dir_name} does not exist"
    
    def test_config_files(self):
        """Test that configuration files exist."""
        project_root = Path(__file__).parent.parent
        
        required_files = [
            "requirements.txt",
            "config/config.yaml",
            ".env.example"
        ]
        
        for file_name in required_files:
            file_path = project_root / file_name
            assert file_path.exists(), f"Required file {file_name} does not exist"


if __name__ == "__main__":
    pytest.main([__file__])
