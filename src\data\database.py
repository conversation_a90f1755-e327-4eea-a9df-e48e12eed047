"""
Database management for WhatsApp AI Assistant.
Handles database connections, migrations, and basic operations.
"""

import os
from contextlib import contextmanager
from typing import Optional, List, Dict, Any, Generator
from sqlalchemy import create_engine, text, event
from sqlalchemy.orm import sessionmaker, Session
from sqlalchemy.pool import StaticPool
from pathlib import Path

from data.models import Base, Contact, Message, Conversation, UserPattern, ScheduledMessage, MemoryContext, AIResponse, SystemLog
from utils.config import get_config
from utils.logging import get_database_logger

logger = get_database_logger()


class DatabaseManager:
    """Manages database connections and operations."""
    
    def __init__(self, database_url: Optional[str] = None):
        self.config = get_config()
        self.database_url = database_url or self.config.database.url
        
        # Ensure data directory exists
        if self.database_url.startswith('sqlite:///'):
            db_path = Path(self.database_url.replace('sqlite:///', ''))
            db_path.parent.mkdir(parents=True, exist_ok=True)
        
        # Create engine
        self.engine = self._create_engine()
        
        # Create session factory
        self.SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=self.engine)
        
        # Initialize database
        self._initialize_database()
    
    def _create_engine(self):
        """Create SQLAlchemy engine with appropriate configuration."""
        engine_kwargs = {
            'echo': self.config.debug,
            'future': True
        }
        
        # SQLite-specific configuration
        if self.database_url.startswith('sqlite'):
            engine_kwargs.update({
                'poolclass': StaticPool,
                'connect_args': {
                    'check_same_thread': False,
                    'timeout': 30
                }
            })
        
        engine = create_engine(self.database_url, **engine_kwargs)
        
        # Enable SQLite FTS5 if configured
        if self.database_url.startswith('sqlite') and self.config.database.enable_fts:
            @event.listens_for(engine, "connect")
            def enable_fts(dbapi_connection, connection_record):
                # Enable FTS5 extension
                dbapi_connection.execute("PRAGMA foreign_keys=ON")
                try:
                    dbapi_connection.execute("SELECT fts5()")
                    logger.info("FTS5 extension is available")
                except Exception as e:
                    logger.warning(f"FTS5 extension not available: {e}")
        
        return engine
    
    def _initialize_database(self):
        """Initialize database schema and FTS tables."""
        try:
            # Create all tables
            Base.metadata.create_all(bind=self.engine)
            logger.info("Database tables created successfully")
            
            # Create FTS tables if enabled
            if self.config.database.enable_fts:
                self._create_fts_tables()
            
        except Exception as e:
            logger.error(f"Failed to initialize database: {e}")
            raise
    
    def _create_fts_tables(self):
        """Create Full-Text Search tables."""
        try:
            with self.engine.connect() as conn:
                # FTS table for messages
                conn.execute(text("""
                    CREATE VIRTUAL TABLE IF NOT EXISTS messages_fts USING fts5(
                        message_id UNINDEXED,
                        message_text,
                        sender_name,
                        content='messages',
                        content_rowid='id'
                    )
                """))
                
                # FTS table for contacts
                conn.execute(text("""
                    CREATE VIRTUAL TABLE IF NOT EXISTS contacts_fts USING fts5(
                        contact_id UNINDEXED,
                        name,
                        nickname,
                        notes,
                        content='contacts',
                        content_rowid='id'
                    )
                """))
                
                # FTS table for memory context
                conn.execute(text("""
                    CREATE VIRTUAL TABLE IF NOT EXISTS memory_fts USING fts5(
                        memory_id UNINDEXED,
                        summary,
                        context_key,
                        content='memory_context',
                        content_rowid='id'
                    )
                """))
                
                # Create triggers to keep FTS tables in sync
                self._create_fts_triggers(conn)
                
                conn.commit()
                logger.info("FTS tables created successfully")
                
        except Exception as e:
            logger.error(f"Failed to create FTS tables: {e}")
    
    def _create_fts_triggers(self, conn):
        """Create triggers to keep FTS tables synchronized."""
        # Messages FTS triggers
        conn.execute(text("""
            CREATE TRIGGER IF NOT EXISTS messages_fts_insert AFTER INSERT ON messages BEGIN
                INSERT INTO messages_fts(message_id, message_text, sender_name)
                VALUES (new.id, new.message_text, new.sender_name);
            END
        """))
        
        conn.execute(text("""
            CREATE TRIGGER IF NOT EXISTS messages_fts_update AFTER UPDATE ON messages BEGIN
                UPDATE messages_fts SET 
                    message_text = new.message_text,
                    sender_name = new.sender_name
                WHERE message_id = new.id;
            END
        """))
        
        conn.execute(text("""
            CREATE TRIGGER IF NOT EXISTS messages_fts_delete AFTER DELETE ON messages BEGIN
                DELETE FROM messages_fts WHERE message_id = old.id;
            END
        """))
        
        # Contacts FTS triggers
        conn.execute(text("""
            CREATE TRIGGER IF NOT EXISTS contacts_fts_insert AFTER INSERT ON contacts BEGIN
                INSERT INTO contacts_fts(contact_id, name, nickname, notes)
                VALUES (new.id, new.name, new.nickname, new.notes);
            END
        """))
        
        conn.execute(text("""
            CREATE TRIGGER IF NOT EXISTS contacts_fts_update AFTER UPDATE ON contacts BEGIN
                UPDATE contacts_fts SET 
                    name = new.name,
                    nickname = new.nickname,
                    notes = new.notes
                WHERE contact_id = new.id;
            END
        """))
        
        conn.execute(text("""
            CREATE TRIGGER IF NOT EXISTS contacts_fts_delete AFTER DELETE ON contacts BEGIN
                DELETE FROM contacts_fts WHERE contact_id = old.id;
            END
        """))
    
    @contextmanager
    def get_session(self) -> Generator[Session, None, None]:
        """Get database session with automatic cleanup."""
        session = self.SessionLocal()
        try:
            yield session
            session.commit()
        except Exception as e:
            session.rollback()
            logger.error(f"Database session error: {e}")
            raise
        finally:
            session.close()
    
    def get_session_sync(self) -> Session:
        """Get database session (manual management required)."""
        return self.SessionLocal()
    
    def health_check(self) -> bool:
        """Check database connectivity."""
        try:
            with self.get_session() as session:
                session.execute(text("SELECT 1"))
                return True
        except Exception as e:
            logger.error(f"Database health check failed: {e}")
            return False

    def search_messages(self, query: str, limit: int = 50) -> List[Dict[str, Any]]:
        """Search messages using FTS if available."""
        try:
            with self.get_session() as session:
                if self.config.database.enable_fts:
                    # Use FTS search
                    result = session.execute(text("""
                        SELECT m.id, m.message_text, m.sender_name, m.timestamp, c.name, c.phone_number
                        FROM messages_fts mf
                        JOIN messages m ON m.id = mf.message_id
                        JOIN contacts c ON c.id = m.contact_id
                        WHERE messages_fts MATCH :query
                        ORDER BY rank
                        LIMIT :limit
                    """), {"query": query, "limit": limit})
                else:
                    # Fallback to LIKE search
                    result = session.execute(text("""
                        SELECT m.id, m.message_text, m.sender_name, m.timestamp, c.name, c.phone_number
                        FROM messages m
                        JOIN contacts c ON c.id = m.contact_id
                        WHERE m.message_text LIKE :query
                        ORDER BY m.timestamp DESC
                        LIMIT :limit
                    """), {"query": f"%{query}%", "limit": limit})

                return [dict(row._mapping) for row in result]

        except Exception as e:
            logger.error(f"Message search failed: {e}")
            return []

    def search_contacts(self, query: str, limit: int = 20) -> List[Dict[str, Any]]:
        """Search contacts using FTS if available."""
        try:
            with self.get_session() as session:
                if self.config.database.enable_fts:
                    # Use FTS search
                    result = session.execute(text("""
                        SELECT c.id, c.phone_number, c.name, c.nickname, c.category
                        FROM contacts_fts cf
                        JOIN contacts c ON c.id = cf.contact_id
                        WHERE contacts_fts MATCH :query
                        ORDER BY rank
                        LIMIT :limit
                    """), {"query": query, "limit": limit})
                else:
                    # Fallback to LIKE search
                    result = session.execute(text("""
                        SELECT id, phone_number, name, nickname, category
                        FROM contacts
                        WHERE name LIKE :query OR nickname LIKE :query OR notes LIKE :query
                        ORDER BY name
                        LIMIT :limit
                    """), {"query": f"%{query}%", "limit": limit})

                return [dict(row._mapping) for row in result]

        except Exception as e:
            logger.error(f"Contact search failed: {e}")
            return []

    def get_database_stats(self) -> Dict[str, Any]:
        """Get database statistics."""
        try:
            with self.get_session() as session:
                stats = {}

                # Count records in each table
                tables = ['contacts', 'messages', 'conversations', 'user_patterns',
                         'scheduled_messages', 'memory_context', 'ai_responses', 'system_logs']

                for table in tables:
                    result = session.execute(text(f"SELECT COUNT(*) FROM {table}"))
                    stats[f"{table}_count"] = result.scalar()

                # Additional statistics
                result = session.execute(text("""
                    SELECT
                        COUNT(DISTINCT contact_id) as active_contacts,
                        COUNT(*) as total_messages,
                        AVG(LENGTH(message_text)) as avg_message_length
                    FROM messages
                    WHERE timestamp > datetime('now', '-30 days')
                """))
                row = result.fetchone()
                if row:
                    stats.update(dict(row._mapping))

                return stats

        except Exception as e:
            logger.error(f"Failed to get database stats: {e}")
            return {}

    def cleanup_old_data(self, retention_days: int = None) -> Dict[str, int]:
        """Clean up old data based on retention policy."""
        if retention_days is None:
            retention_days = self.config.security.data_retention_days

        cleanup_stats = {}

        try:
            with self.get_session() as session:
                cutoff_date = text(f"datetime('now', '-{retention_days} days')")

                # Clean up old system logs
                result = session.execute(text(f"""
                    DELETE FROM system_logs
                    WHERE timestamp < {cutoff_date} AND severity NOT IN ('error', 'critical')
                """))
                cleanup_stats['system_logs_deleted'] = result.rowcount

                # Clean up old AI responses
                result = session.execute(text(f"""
                    DELETE FROM ai_responses
                    WHERE created_at < {cutoff_date} AND status = 'rejected'
                """))
                cleanup_stats['ai_responses_deleted'] = result.rowcount

                # Clean up expired memory contexts
                result = session.execute(text("""
                    DELETE FROM memory_context
                    WHERE expires_at IS NOT NULL AND expires_at < datetime('now')
                """))
                cleanup_stats['expired_memories_deleted'] = result.rowcount

                # Clean up old scheduled messages
                result = session.execute(text(f"""
                    DELETE FROM scheduled_messages
                    WHERE created_at < {cutoff_date} AND status IN ('sent', 'failed', 'cancelled')
                """))
                cleanup_stats['old_scheduled_messages_deleted'] = result.rowcount

                session.commit()
                logger.info(f"Data cleanup completed: {cleanup_stats}")

        except Exception as e:
            logger.error(f"Data cleanup failed: {e}")
            cleanup_stats['error'] = str(e)

        return cleanup_stats

    def backup_database(self, backup_path: Optional[str] = None) -> bool:
        """Create database backup (SQLite only)."""
        if not self.database_url.startswith('sqlite'):
            logger.warning("Database backup only supported for SQLite")
            return False

        try:
            import shutil
            from datetime import datetime

            # Get source database path
            source_path = self.database_url.replace('sqlite:///', '')

            # Generate backup path if not provided
            if backup_path is None:
                timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
                backup_dir = Path('data/backups')
                backup_dir.mkdir(parents=True, exist_ok=True)
                backup_path = backup_dir / f"whatsapp_assistant_backup_{timestamp}.db"

            # Create backup
            shutil.copy2(source_path, backup_path)
            logger.info(f"Database backup created: {backup_path}")
            return True

        except Exception as e:
            logger.error(f"Database backup failed: {e}")
            return False


# Global database manager instance
db_manager = None


def get_database_manager() -> DatabaseManager:
    """Get the global database manager instance."""
    global db_manager
    if db_manager is None:
        db_manager = DatabaseManager()
    return db_manager


def init_database(database_url: Optional[str] = None) -> DatabaseManager:
    """Initialize the global database manager."""
    global db_manager
    db_manager = DatabaseManager(database_url)
    return db_manager
