"""
Helper utilities for WhatsApp AI Assistant.
Common functions used across the application.
"""

import re
import hashlib
import secrets
import asyncio
from datetime import datetime, timedelta
from typing import Any, Dict, List, Optional, Union, Callable
from pathlib import Path
import json
import base64
from cryptography.fernet import <PERSON><PERSON><PERSON>
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC

from .logging import get_logger

logger = get_logger("helpers")


class TextProcessor:
    """Text processing utilities."""
    
    @staticmethod
    def clean_text(text: str) -> str:
        """Clean and normalize text."""
        if not text:
            return ""
        
        # Remove extra whitespace
        text = re.sub(r'\s+', ' ', text.strip())
        
        # Remove control characters except newlines and tabs
        text = re.sub(r'[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]', '', text)
        
        return text
    
    @staticmethod
    def extract_phone_numbers(text: str) -> List[str]:
        """Extract phone numbers from text."""
        # Pattern for international phone numbers
        pattern = r'(\+?\d{1,4}[-.\s]?)?\(?\d{1,4}\)?[-.\s]?\d{1,4}[-.\s]?\d{1,9}'
        matches = re.findall(pattern, text)
        
        # Clean and validate phone numbers
        phone_numbers = []
        for match in matches:
            phone = re.sub(r'[-.\s()]', '', match)
            if len(phone) >= 7:  # Minimum valid phone number length
                phone_numbers.append(phone)
        
        return phone_numbers
    
    @staticmethod
    def extract_emails(text: str) -> List[str]:
        """Extract email addresses from text."""
        pattern = r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b'
        return re.findall(pattern, text)
    
    @staticmethod
    def extract_urls(text: str) -> List[str]:
        """Extract URLs from text."""
        pattern = r'http[s]?://(?:[a-zA-Z]|[0-9]|[$-_@.&+]|[!*\\(\\),]|(?:%[0-9a-fA-F][0-9a-fA-F]))+'
        return re.findall(pattern, text)
    
    @staticmethod
    def count_emojis(text: str) -> int:
        """Count emojis in text."""
        emoji_pattern = re.compile(
            "["
            "\U0001F600-\U0001F64F"  # emoticons
            "\U0001F300-\U0001F5FF"  # symbols & pictographs
            "\U0001F680-\U0001F6FF"  # transport & map symbols
            "\U0001F1E0-\U0001F1FF"  # flags (iOS)
            "\U00002702-\U000027B0"
            "\U000024C2-\U0001F251"
            "]+",
            flags=re.UNICODE
        )
        return len(emoji_pattern.findall(text))
    
    @staticmethod
    def get_text_stats(text: str) -> Dict[str, Any]:
        """Get comprehensive text statistics."""
        if not text:
            return {
                'length': 0,
                'words': 0,
                'sentences': 0,
                'emojis': 0,
                'urls': 0,
                'emails': 0,
                'phone_numbers': 0
            }
        
        return {
            'length': len(text),
            'words': len(text.split()),
            'sentences': len(re.split(r'[.!?]+', text)),
            'emojis': TextProcessor.count_emojis(text),
            'urls': len(TextProcessor.extract_urls(text)),
            'emails': len(TextProcessor.extract_emails(text)),
            'phone_numbers': len(TextProcessor.extract_phone_numbers(text))
        }


class CryptoHelper:
    """Cryptographic utilities."""
    
    @staticmethod
    def generate_key() -> str:
        """Generate a new encryption key."""
        return Fernet.generate_key().decode()
    
    @staticmethod
    def derive_key_from_password(password: str, salt: Optional[bytes] = None) -> tuple[str, bytes]:
        """Derive encryption key from password."""
        if salt is None:
            salt = secrets.token_bytes(16)
        
        kdf = PBKDF2HMAC(
            algorithm=hashes.SHA256(),
            length=32,
            salt=salt,
            iterations=100000,
        )
        key = base64.urlsafe_b64encode(kdf.derive(password.encode()))
        return key.decode(), salt
    
    @staticmethod
    def encrypt_text(text: str, key: str) -> str:
        """Encrypt text using Fernet encryption."""
        try:
            f = Fernet(key.encode())
            encrypted = f.encrypt(text.encode())
            return base64.urlsafe_b64encode(encrypted).decode()
        except Exception as e:
            logger.error(f"Encryption failed: {e}")
            raise
    
    @staticmethod
    def decrypt_text(encrypted_text: str, key: str) -> str:
        """Decrypt text using Fernet encryption."""
        try:
            f = Fernet(key.encode())
            encrypted_bytes = base64.urlsafe_b64decode(encrypted_text.encode())
            decrypted = f.decrypt(encrypted_bytes)
            return decrypted.decode()
        except Exception as e:
            logger.error(f"Decryption failed: {e}")
            raise
    
    @staticmethod
    def hash_text(text: str, salt: Optional[str] = None) -> str:
        """Create SHA-256 hash of text."""
        if salt:
            text = text + salt
        return hashlib.sha256(text.encode()).hexdigest()
    
    @staticmethod
    def generate_token(length: int = 32) -> str:
        """Generate a secure random token."""
        return secrets.token_urlsafe(length)


class DateTimeHelper:
    """Date and time utilities."""
    
    @staticmethod
    def now() -> datetime:
        """Get current datetime."""
        return datetime.now()
    
    @staticmethod
    def format_datetime(dt: datetime, format_str: str = "%Y-%m-%d %H:%M:%S") -> str:
        """Format datetime to string."""
        return dt.strftime(format_str)
    
    @staticmethod
    def parse_datetime(dt_str: str, format_str: str = "%Y-%m-%d %H:%M:%S") -> datetime:
        """Parse datetime from string."""
        return datetime.strptime(dt_str, format_str)
    
    @staticmethod
    def time_ago(dt: datetime) -> str:
        """Get human-readable time difference."""
        now = datetime.now()
        diff = now - dt
        
        if diff.days > 0:
            return f"{diff.days} days ago"
        elif diff.seconds > 3600:
            hours = diff.seconds // 3600
            return f"{hours} hours ago"
        elif diff.seconds > 60:
            minutes = diff.seconds // 60
            return f"{minutes} minutes ago"
        else:
            return "just now"
    
    @staticmethod
    def is_business_hours(dt: Optional[datetime] = None, 
                         start_hour: int = 9, end_hour: int = 18) -> bool:
        """Check if datetime is within business hours."""
        if dt is None:
            dt = datetime.now()
        
        # Check if it's a weekday (Monday = 0, Sunday = 6)
        if dt.weekday() >= 5:  # Saturday or Sunday
            return False
        
        # Check if it's within business hours
        return start_hour <= dt.hour < end_hour


class FileHelper:
    """File system utilities."""
    
    @staticmethod
    def ensure_directory(path: Union[str, Path]) -> Path:
        """Ensure directory exists, create if it doesn't."""
        path = Path(path)
        path.mkdir(parents=True, exist_ok=True)
        return path
    
    @staticmethod
    def safe_filename(filename: str) -> str:
        """Create a safe filename by removing invalid characters."""
        # Remove invalid characters
        filename = re.sub(r'[<>:"/\\|?*]', '_', filename)
        # Remove leading/trailing dots and spaces
        filename = filename.strip('. ')
        # Limit length
        if len(filename) > 255:
            filename = filename[:255]
        return filename
    
    @staticmethod
    def get_file_size(path: Union[str, Path]) -> int:
        """Get file size in bytes."""
        return Path(path).stat().st_size
    
    @staticmethod
    def read_json(path: Union[str, Path]) -> Dict[str, Any]:
        """Read JSON file."""
        with open(path, 'r', encoding='utf-8') as f:
            return json.load(f)
    
    @staticmethod
    def write_json(data: Dict[str, Any], path: Union[str, Path], indent: int = 2):
        """Write JSON file."""
        FileHelper.ensure_directory(Path(path).parent)
        with open(path, 'w', encoding='utf-8') as f:
            json.dump(data, f, indent=indent, ensure_ascii=False)


class AsyncHelper:
    """Async utilities."""
    
    @staticmethod
    async def run_with_timeout(coro, timeout: float):
        """Run coroutine with timeout."""
        try:
            return await asyncio.wait_for(coro, timeout=timeout)
        except asyncio.TimeoutError:
            logger.warning(f"Operation timed out after {timeout} seconds")
            raise
    
    @staticmethod
    async def retry_async(func: Callable, max_retries: int = 3, 
                         delay: float = 1.0, backoff: float = 2.0):
        """Retry async function with exponential backoff."""
        for attempt in range(max_retries):
            try:
                return await func()
            except Exception as e:
                if attempt == max_retries - 1:
                    raise
                
                wait_time = delay * (backoff ** attempt)
                logger.warning(f"Attempt {attempt + 1} failed: {e}. Retrying in {wait_time}s")
                await asyncio.sleep(wait_time)


class ValidationHelper:
    """Validation utilities."""
    
    @staticmethod
    def is_valid_phone(phone: str) -> bool:
        """Validate phone number format."""
        # Remove all non-digit characters
        digits = re.sub(r'\D', '', phone)
        # Check if it's a reasonable length for a phone number
        return 7 <= len(digits) <= 15
    
    @staticmethod
    def is_valid_email(email: str) -> bool:
        """Validate email format."""
        pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
        return bool(re.match(pattern, email))
    
    @staticmethod
    def sanitize_input(text: str, max_length: int = 1000) -> str:
        """Sanitize user input."""
        if not text:
            return ""
        
        # Clean text
        text = TextProcessor.clean_text(text)
        
        # Limit length
        if len(text) > max_length:
            text = text[:max_length]
        
        return text


# Convenience functions
def clean_text(text: str) -> str:
    """Clean and normalize text."""
    return TextProcessor.clean_text(text)


def get_text_stats(text: str) -> Dict[str, Any]:
    """Get text statistics."""
    return TextProcessor.get_text_stats(text)


def encrypt_text(text: str, key: str) -> str:
    """Encrypt text."""
    return CryptoHelper.encrypt_text(text, key)


def decrypt_text(encrypted_text: str, key: str) -> str:
    """Decrypt text."""
    return CryptoHelper.decrypt_text(encrypted_text, key)


def time_ago(dt: datetime) -> str:
    """Get human-readable time difference."""
    return DateTimeHelper.time_ago(dt)
