# coding: utf-8

"""
    Kubernetes

    No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)  # noqa: E501

    The version of the OpenAPI document: release-1.33
    Generated by: https://openapi-generator.tech
"""


import pprint
import re  # noqa: F401

import six

from kubernetes.client.configuration import Configuration


class V1alpha1Mutation(object):
    """NOTE: This class is auto generated by OpenAPI Generator.
    Ref: https://openapi-generator.tech

    Do not edit the class manually.
    """

    """
    Attributes:
      openapi_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    openapi_types = {
        'apply_configuration': 'V1alpha1ApplyConfiguration',
        'json_patch': 'V1alpha1JSONPatch',
        'patch_type': 'str'
    }

    attribute_map = {
        'apply_configuration': 'applyConfiguration',
        'json_patch': 'jsonPatch',
        'patch_type': 'patchType'
    }

    def __init__(self, apply_configuration=None, json_patch=None, patch_type=None, local_vars_configuration=None):  # noqa: E501
        """V1alpha1Mutation - a model defined in OpenAPI"""  # noqa: E501
        if local_vars_configuration is None:
            local_vars_configuration = Configuration()
        self.local_vars_configuration = local_vars_configuration

        self._apply_configuration = None
        self._json_patch = None
        self._patch_type = None
        self.discriminator = None

        if apply_configuration is not None:
            self.apply_configuration = apply_configuration
        if json_patch is not None:
            self.json_patch = json_patch
        self.patch_type = patch_type

    @property
    def apply_configuration(self):
        """Gets the apply_configuration of this V1alpha1Mutation.  # noqa: E501


        :return: The apply_configuration of this V1alpha1Mutation.  # noqa: E501
        :rtype: V1alpha1ApplyConfiguration
        """
        return self._apply_configuration

    @apply_configuration.setter
    def apply_configuration(self, apply_configuration):
        """Sets the apply_configuration of this V1alpha1Mutation.


        :param apply_configuration: The apply_configuration of this V1alpha1Mutation.  # noqa: E501
        :type: V1alpha1ApplyConfiguration
        """

        self._apply_configuration = apply_configuration

    @property
    def json_patch(self):
        """Gets the json_patch of this V1alpha1Mutation.  # noqa: E501


        :return: The json_patch of this V1alpha1Mutation.  # noqa: E501
        :rtype: V1alpha1JSONPatch
        """
        return self._json_patch

    @json_patch.setter
    def json_patch(self, json_patch):
        """Sets the json_patch of this V1alpha1Mutation.


        :param json_patch: The json_patch of this V1alpha1Mutation.  # noqa: E501
        :type: V1alpha1JSONPatch
        """

        self._json_patch = json_patch

    @property
    def patch_type(self):
        """Gets the patch_type of this V1alpha1Mutation.  # noqa: E501

        patchType indicates the patch strategy used. Allowed values are \"ApplyConfiguration\" and \"JSONPatch\". Required.  # noqa: E501

        :return: The patch_type of this V1alpha1Mutation.  # noqa: E501
        :rtype: str
        """
        return self._patch_type

    @patch_type.setter
    def patch_type(self, patch_type):
        """Sets the patch_type of this V1alpha1Mutation.

        patchType indicates the patch strategy used. Allowed values are \"ApplyConfiguration\" and \"JSONPatch\". Required.  # noqa: E501

        :param patch_type: The patch_type of this V1alpha1Mutation.  # noqa: E501
        :type: str
        """
        if self.local_vars_configuration.client_side_validation and patch_type is None:  # noqa: E501
            raise ValueError("Invalid value for `patch_type`, must not be `None`")  # noqa: E501

        self._patch_type = patch_type

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.openapi_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, V1alpha1Mutation):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, V1alpha1Mutation):
            return True

        return self.to_dict() != other.to_dict()
