# Getting Started with WhatsApp AI Assistant

This guide will help you set up and start using the WhatsApp AI Assistant.

## 🚀 Quick Start

### Prerequisites

- **Python 3.8+** - Required for the main application
- **Node.js 16+** - Required for WhatsApp Web.js integration
- **Git** - For version control

### 1. Initial Setup

1. **<PERSON>lone or download the project** (if not already done)

2. **Run the setup script:**
   ```bash
   python scripts/setup.py
   ```
   
   This will:
   - Check system requirements
   - Create a virtual environment
   - Install Python dependencies
   - Install WhatsApp Web.js dependencies
   - Create configuration files

### 2. Configuration

1. **Edit the `.env` file** with your API keys and settings:
   ```bash
   # Required: OpenAI API Key
   OPENAI_API_KEY=your_openai_api_key_here
   
   # Required: Telegram <PERSON><PERSON> (get from @BotFather)
   TELEGRAM_BOT_TOKEN=your_telegram_bot_token_here
   
   # Required: Your Telegram User ID (get from @userinfobot)
   TELEGRAM_USER_ID=your_telegram_user_id_here
   ```

2. **Optional: Customize `config/config.yaml`** for advanced settings

### 3. First Run

1. **Activate the virtual environment:**
   - Windows: `venv\Scripts\activate`
   - Linux/Mac: `source venv/bin/activate`

2. **Start the application:**
   ```bash
   python src/main.py
   ```

3. **For development with auto-reload:**
   ```bash
   python scripts/dev.py dev
   ```

## 📁 Project Structure

```
whatsapp-ai-assistant/
├── src/                    # Main application code
│   ├── core/              # AI engine, memory management
│   ├── integrations/      # WhatsApp, Telegram, OpenAI clients
│   ├── data/             # Database models and management
│   ├── scheduling/       # Task scheduling
│   ├── utils/            # Configuration, logging, helpers
│   └── main.py           # Application entry point
├── config/               # Configuration files
├── data/                 # Database and data storage
├── logs/                 # Application logs
├── tests/                # Test suites
├── scripts/              # Setup and development scripts
└── docs/                 # Documentation
```

## 🔧 Development Tools

The project includes several development tools accessible via `scripts/dev.py`:

```bash
# Run tests
python scripts/dev.py test

# Run tests with coverage
python scripts/dev.py test --coverage

# Format code
python scripts/dev.py format

# Lint code
python scripts/dev.py lint

# Check dependencies
python scripts/dev.py deps

# Clean cache files
python scripts/dev.py clean

# Start development server
python scripts/dev.py dev

# Show project statistics
python scripts/dev.py stats

# Backup database
python scripts/dev.py backup
```

## 🔑 API Keys Setup

### OpenAI API Key
1. Go to [OpenAI API](https://platform.openai.com/api-keys)
2. Create a new API key
3. Add it to your `.env` file as `OPENAI_API_KEY`

### Telegram Bot Token
1. Message [@BotFather](https://t.me/BotFather) on Telegram
2. Create a new bot with `/newbot`
3. Copy the bot token to your `.env` file as `TELEGRAM_BOT_TOKEN`

### Your Telegram User ID
1. Message [@userinfobot](https://t.me/userinfobot) on Telegram
2. Copy your user ID to your `.env` file as `TELEGRAM_USER_ID`

## 🗄️ Database

The application uses SQLite by default with the following features:
- **Full-text search** for messages and contacts
- **Automatic backups** (configurable)
- **Data retention policies** for privacy
- **Encryption support** for sensitive data

Database file location: `data/whatsapp_assistant.db`

## 📝 Logging

Logs are stored in the `logs/` directory:
- `app.log` - Main application logs
- `whatsapp.log` - WhatsApp integration logs
- `telegram.log` - Telegram bot logs
- `ai.log` - AI engine logs
- `scheduler.log` - Task scheduler logs

## 🧪 Testing

Run the test suite to verify everything is working:

```bash
# Basic tests
python scripts/dev.py test

# Tests with coverage report
python scripts/dev.py test --coverage

# Run specific test file
python -m pytest tests/test_basic_setup.py -v
```

## 🔒 Security & Privacy

- **Local data storage** - All data stays on your machine
- **Encryption support** - Sensitive data can be encrypted
- **Configurable retention** - Automatic cleanup of old data
- **Access control** - Only your Telegram account can control the bot

## 🚨 Troubleshooting

### Common Issues

1. **"Module not found" errors**
   - Make sure virtual environment is activated
   - Run `pip install -r requirements.txt`

2. **WhatsApp connection issues**
   - Ensure Node.js is installed
   - Check that WhatsApp Web works in your browser
   - Clear WhatsApp session data in `data/whatsapp_session/`

3. **Database errors**
   - Check file permissions in `data/` directory
   - Try deleting and recreating the database

4. **API key errors**
   - Verify API keys in `.env` file
   - Check API key permissions and quotas

### Getting Help

1. Check the logs in `logs/` directory
2. Run tests to identify issues: `python scripts/dev.py test`
3. Enable debug mode in `.env`: `DEBUG=true`

## 📚 Next Steps

Once you have the basic setup working:

1. **Configure WhatsApp Integration** - Set up WhatsApp Web.js
2. **Set up Telegram Bot** - Configure the control interface
3. **Train the AI** - Let it learn your communication patterns
4. **Configure Scheduling** - Set up automated messages and reminders
5. **Customize Behavior** - Adjust AI responses and automation rules

## 🔄 Updates

To update the application:

1. Pull latest changes (if using Git)
2. Update dependencies: `pip install -r requirements.txt --upgrade`
3. Run database migrations (when available)
4. Restart the application

## 📖 Additional Documentation

- [Configuration Guide](CONFIG.md) - Detailed configuration options
- [API Reference](API.md) - API documentation
- [Development Guide](DEVELOPMENT.md) - Contributing and development
- [Troubleshooting](TROUBLESHOOTING.md) - Common issues and solutions
