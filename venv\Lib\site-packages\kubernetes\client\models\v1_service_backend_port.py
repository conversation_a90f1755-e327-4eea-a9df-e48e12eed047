# coding: utf-8

"""
    Kubernetes

    No description provided (generated by Openapi Generator https://github.com/openapitools/openapi-generator)  # noqa: E501

    The version of the OpenAPI document: release-1.33
    Generated by: https://openapi-generator.tech
"""


import pprint
import re  # noqa: F401

import six

from kubernetes.client.configuration import Configuration


class V1ServiceBackendPort(object):
    """NOTE: This class is auto generated by OpenAPI Generator.
    Ref: https://openapi-generator.tech

    Do not edit the class manually.
    """

    """
    Attributes:
      openapi_types (dict): The key is attribute name
                            and the value is attribute type.
      attribute_map (dict): The key is attribute name
                            and the value is json key in definition.
    """
    openapi_types = {
        'name': 'str',
        'number': 'int'
    }

    attribute_map = {
        'name': 'name',
        'number': 'number'
    }

    def __init__(self, name=None, number=None, local_vars_configuration=None):  # noqa: E501
        """V1ServiceBackendPort - a model defined in OpenAPI"""  # noqa: E501
        if local_vars_configuration is None:
            local_vars_configuration = Configuration()
        self.local_vars_configuration = local_vars_configuration

        self._name = None
        self._number = None
        self.discriminator = None

        if name is not None:
            self.name = name
        if number is not None:
            self.number = number

    @property
    def name(self):
        """Gets the name of this V1ServiceBackendPort.  # noqa: E501

        name is the name of the port on the Service. This is a mutually exclusive setting with \"Number\".  # noqa: E501

        :return: The name of this V1ServiceBackendPort.  # noqa: E501
        :rtype: str
        """
        return self._name

    @name.setter
    def name(self, name):
        """Sets the name of this V1ServiceBackendPort.

        name is the name of the port on the Service. This is a mutually exclusive setting with \"Number\".  # noqa: E501

        :param name: The name of this V1ServiceBackendPort.  # noqa: E501
        :type: str
        """

        self._name = name

    @property
    def number(self):
        """Gets the number of this V1ServiceBackendPort.  # noqa: E501

        number is the numerical port number (e.g. 80) on the Service. This is a mutually exclusive setting with \"Name\".  # noqa: E501

        :return: The number of this V1ServiceBackendPort.  # noqa: E501
        :rtype: int
        """
        return self._number

    @number.setter
    def number(self, number):
        """Sets the number of this V1ServiceBackendPort.

        number is the numerical port number (e.g. 80) on the Service. This is a mutually exclusive setting with \"Name\".  # noqa: E501

        :param number: The number of this V1ServiceBackendPort.  # noqa: E501
        :type: int
        """

        self._number = number

    def to_dict(self):
        """Returns the model properties as a dict"""
        result = {}

        for attr, _ in six.iteritems(self.openapi_types):
            value = getattr(self, attr)
            if isinstance(value, list):
                result[attr] = list(map(
                    lambda x: x.to_dict() if hasattr(x, "to_dict") else x,
                    value
                ))
            elif hasattr(value, "to_dict"):
                result[attr] = value.to_dict()
            elif isinstance(value, dict):
                result[attr] = dict(map(
                    lambda item: (item[0], item[1].to_dict())
                    if hasattr(item[1], "to_dict") else item,
                    value.items()
                ))
            else:
                result[attr] = value

        return result

    def to_str(self):
        """Returns the string representation of the model"""
        return pprint.pformat(self.to_dict())

    def __repr__(self):
        """For `print` and `pprint`"""
        return self.to_str()

    def __eq__(self, other):
        """Returns true if both objects are equal"""
        if not isinstance(other, V1ServiceBackendPort):
            return False

        return self.to_dict() == other.to_dict()

    def __ne__(self, other):
        """Returns true if both objects are not equal"""
        if not isinstance(other, V1ServiceBackendPort):
            return True

        return self.to_dict() != other.to_dict()
