"""
Telegram Bot for WhatsApp AI Assistant.
Provides control interface and message confirmation system via Telegram.
"""

import asyncio
from datetime import datetime
from typing import Dict, Any, Optional, List, Callable
import json

from telegram import Update, InlineKeyboardButton, InlineKeyboardMarkup, BotCommand
from telegram.ext import (
    Application, CommandHandler, CallbackQueryHandler, MessageHandler,
    ContextTypes, filters
)
from telegram.constants import ParseMode

from utils.logging import get_telegram_logger
from utils.config import get_config
from data.database import get_database_manager
from data.models import Contact, Message, SystemLog

logger = get_telegram_logger()


class TelegramBot:
    """Telegram bot for WhatsApp AI Assistant control."""
    
    def __init__(self, whatsapp_manager=None):
        self.config = get_config()
        self.db_manager = get_database_manager()
        self.whatsapp_manager = whatsapp_manager
        
        # Bot configuration
        self.bot_token = self.config.telegram.bot_token
        self.authorized_user_id = int(self.config.telegram.user_id)
        
        # Application and handlers
        self.application: Optional[Application] = None
        self.is_running = False
        
        # Message confirmation system
        self.pending_confirmations: Dict[str, Dict[str, Any]] = {}
        self.confirmation_timeout = self.config.telegram.notification_timeout_seconds
        
        # Callbacks
        self.message_approved_callback: Optional[Callable] = None
        self.message_rejected_callback: Optional[Callable] = None
        
        logger.info("Telegram bot initialized")
    
    async def initialize(self) -> bool:
        """Initialize the Telegram bot."""
        try:
            if not self.bot_token:
                logger.error("Telegram bot token not configured")
                return False
            
            if not self.authorized_user_id:
                logger.error("Authorized user ID not configured")
                return False
            
            # Create application
            self.application = Application.builder().token(self.bot_token).build()
            
            # Setup handlers
            self._setup_handlers()
            
            # Setup bot commands
            await self._setup_bot_commands()
            
            logger.info("Telegram bot initialized successfully")
            return True
            
        except Exception as e:
            logger.error(f"Failed to initialize Telegram bot: {e}")
            return False
    
    def _setup_handlers(self):
        """Setup command and callback handlers."""
        # Command handlers
        self.application.add_handler(CommandHandler("start", self._handle_start))
        self.application.add_handler(CommandHandler("help", self._handle_help))
        self.application.add_handler(CommandHandler("status", self._handle_status))
        self.application.add_handler(CommandHandler("settings", self._handle_settings))
        self.application.add_handler(CommandHandler("contacts", self._handle_contacts))
        self.application.add_handler(CommandHandler("chats", self._handle_chats))
        self.application.add_handler(CommandHandler("stats", self._handle_stats))
        self.application.add_handler(CommandHandler("health", self._handle_health))
        
        # Callback query handler for inline keyboards
        self.application.add_handler(CallbackQueryHandler(self._handle_callback_query))
        
        # Message handler for text messages
        self.application.add_handler(
            MessageHandler(filters.TEXT & ~filters.COMMAND, self._handle_message)
        )
        
        logger.debug("Telegram bot handlers configured")
    
    async def _setup_bot_commands(self):
        """Setup bot commands menu."""
        try:
            commands = [
                BotCommand("start", "Start the bot and show welcome message"),
                BotCommand("help", "Show help and available commands"),
                BotCommand("status", "Show WhatsApp connection status"),
                BotCommand("settings", "Show and modify settings"),
                BotCommand("contacts", "List WhatsApp contacts"),
                BotCommand("chats", "List active WhatsApp chats"),
                BotCommand("stats", "Show usage statistics"),
                BotCommand("health", "Perform system health check"),
            ]
            
            await self.application.bot.set_my_commands(commands)
            logger.debug("Bot commands menu configured")
            
        except Exception as e:
            logger.error(f"Failed to setup bot commands: {e}")
    
    def _check_authorization(self, update: Update) -> bool:
        """Check if user is authorized to use the bot."""
        user_id = update.effective_user.id
        
        if user_id != self.authorized_user_id:
            logger.warning(f"Unauthorized access attempt from user {user_id}")
            return False
        
        return True
    
    async def _handle_start(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle /start command."""
        if not self._check_authorization(update):
            await update.message.reply_text("❌ Unauthorized access.")
            return
        
        welcome_message = (
            "🤖 *WhatsApp AI Assistant*\n\n"
            "Welcome! I'm your WhatsApp AI Assistant control bot.\n\n"
            "*Available Commands:*\n"
            "• /status - Check WhatsApp connection\n"
            "• /contacts - List contacts\n"
            "• /chats - List active chats\n"
            "• /stats - Usage statistics\n"
            "• /health - System health check\n"
            "• /settings - Bot settings\n"
            "• /help - Show detailed help\n\n"
            "I'll notify you when messages need confirmation!"
        )
        
        await update.message.reply_text(
            welcome_message,
            parse_mode=ParseMode.MARKDOWN
        )
        
        logger.info(f"Bot started by user {update.effective_user.id}")
    
    async def _handle_help(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle /help command."""
        if not self._check_authorization(update):
            await update.message.reply_text("❌ Unauthorized access.")
            return
        
        help_message = (
            "🤖 *WhatsApp AI Assistant Help*\n\n"
            "*Main Commands:*\n"
            "• `/start` - Start the bot\n"
            "• `/status` - Show WhatsApp connection status\n"
            "• `/health` - Perform system health check\n"
            "• `/stats` - Show usage statistics\n\n"
            "*Contact Management:*\n"
            "• `/contacts` - List all contacts\n"
            "• `/chats` - List active conversations\n\n"
            "*Settings:*\n"
            "• `/settings` - Show current settings\n\n"
            "*Message Confirmation:*\n"
            "When the AI generates a response, you'll receive a notification with:\n"
            "• ✅ *Approve* - Send the message\n"
            "• ✏️ *Edit* - Modify before sending\n"
            "• ❌ *Reject* - Don't send the message\n\n"
            "*Features:*\n"
            "• Automatic message processing\n"
            "• Smart response generation\n"
            "• Contact categorization\n"
            "• Conversation memory\n"
            "• Pattern learning\n\n"
            "Need more help? Contact your administrator."
        )
        
        await update.message.reply_text(
            help_message,
            parse_mode=ParseMode.MARKDOWN
        )
    
    async def _handle_status(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle /status command."""
        if not self._check_authorization(update):
            await update.message.reply_text("❌ Unauthorized access.")
            return
        
        try:
            # Get WhatsApp status
            if self.whatsapp_manager:
                status = self.whatsapp_manager.get_status()
                
                status_icon = "🟢" if status.get('running') and status.get('client_ready') else "🔴"
                auth_icon = "✅" if status.get('authenticated') else "❌"
                
                status_message = (
                    f"📱 *WhatsApp Status* {status_icon}\n\n"
                    f"*Connection:* {status.get('connection_state', 'Unknown').title()}\n"
                    f"*Authenticated:* {auth_icon}\n"
                    f"*Client Ready:* {'✅' if status.get('client_ready') else '❌'}\n"
                    f"*Running:* {'✅' if status.get('running') else '❌'}\n"
                )
                
                if status.get('start_time'):
                    start_time = datetime.fromisoformat(status['start_time'])
                    uptime = datetime.now() - start_time
                    status_message += f"*Uptime:* {str(uptime).split('.')[0]}\n"
                
                # Add connection stats if available
                if 'connection_stats' in status:
                    stats = status['connection_stats']
                    if stats.get('is_healthy'):
                        status_message += "*Health:* 🟢 Healthy\n"
                    else:
                        status_message += "*Health:* 🔴 Issues detected\n"
                
            else:
                status_message = "❌ WhatsApp manager not initialized"
            
            await update.message.reply_text(
                status_message,
                parse_mode=ParseMode.MARKDOWN
            )
            
        except Exception as e:
            logger.error(f"Error getting status: {e}")
            await update.message.reply_text(
                "❌ Error retrieving status information."
            )
    
    async def _handle_settings(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle /settings command."""
        if not self._check_authorization(update):
            await update.message.reply_text("❌ Unauthorized access.")
            return
        
        settings_message = (
            "⚙️ *Bot Settings*\n\n"
            f"*Authorized User:* {self.authorized_user_id}\n"
            f"*Confirmation Timeout:* {self.confirmation_timeout}s\n"
            f"*Auto-delete Notifications:* {self.config.telegram.ui.auto_delete_notifications}\n"
            f"*Message Preview Length:* {self.config.telegram.ui.message_preview_length}\n\n"
            "*WhatsApp Settings:*\n"
            f"*Auto Reconnect:* {self.config.whatsapp.auto_reconnect}\n"
            f"*Ignore Groups:* {self.config.whatsapp.message_processing.get('ignore_groups', False)}\n"
            f"*Process Media:* {self.config.whatsapp.message_processing.get('process_media', False)}\n\n"
            "Use inline buttons below to modify settings."
        )
        
        keyboard = [
            [InlineKeyboardButton("🔄 Refresh", callback_data="settings_refresh")],
            [InlineKeyboardButton("📱 WhatsApp Settings", callback_data="settings_whatsapp")],
            [InlineKeyboardButton("🤖 Bot Settings", callback_data="settings_bot")]
        ]
        reply_markup = InlineKeyboardMarkup(keyboard)
        
        await update.message.reply_text(
            settings_message,
            parse_mode=ParseMode.MARKDOWN,
            reply_markup=reply_markup
        )
    
    async def _handle_contacts(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle /contacts command."""
        if not self._check_authorization(update):
            await update.message.reply_text("❌ Unauthorized access.")
            return
        
        try:
            with self.db_manager.get_session() as session:
                contacts = session.query(Contact).order_by(Contact.name).limit(20).all()
                
                if not contacts:
                    await update.message.reply_text("📱 No contacts found.")
                    return
                
                contacts_message = "📱 *WhatsApp Contacts* (Top 20)\n\n"
                
                for contact in contacts:
                    name = contact.name or "Unknown"
                    category_icon = {
                        'family': '👨‍👩‍👧‍👦',
                        'friends': '👥',
                        'work': '💼',
                        'services': '🔧',
                        'other': '👤'
                    }.get(contact.category, '👤')
                    
                    last_seen = ""
                    if contact.last_seen:
                        last_seen = f" (Last: {contact.last_seen.strftime('%m/%d %H:%M')})"
                    
                    contacts_message += f"{category_icon} *{name}*{last_seen}\n"
                    contacts_message += f"   📞 {contact.phone_number}\n"
                    contacts_message += f"   📂 {contact.category.title()}\n\n"
                
                # Add navigation buttons
                keyboard = [
                    [InlineKeyboardButton("🔄 Refresh", callback_data="contacts_refresh")],
                    [InlineKeyboardButton("📊 Contact Stats", callback_data="contacts_stats")]
                ]
                reply_markup = InlineKeyboardMarkup(keyboard)
                
                await update.message.reply_text(
                    contacts_message,
                    parse_mode=ParseMode.MARKDOWN,
                    reply_markup=reply_markup
                )
                
        except Exception as e:
            logger.error(f"Error getting contacts: {e}")
            await update.message.reply_text("❌ Error retrieving contacts.")
    
    async def _handle_chats(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle /chats command."""
        if not self._check_authorization(update):
            await update.message.reply_text("❌ Unauthorized access.")
            return
        
        # Request chats from WhatsApp
        if self.whatsapp_manager:
            await self.whatsapp_manager.get_chats()
            await update.message.reply_text(
                "📱 Requesting chat list from WhatsApp...\n"
                "This may take a moment."
            )
        else:
            await update.message.reply_text("❌ WhatsApp manager not available.")
    
    async def _handle_stats(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle /stats command."""
        if not self._check_authorization(update):
            await update.message.reply_text("❌ Unauthorized access.")
            return
        
        try:
            # Get database statistics
            db_stats = self.db_manager.get_database_stats()
            
            # Get WhatsApp statistics
            wa_stats = {}
            if self.whatsapp_manager:
                wa_stats = self.whatsapp_manager.get_statistics()
            
            stats_message = (
                "📊 *Usage Statistics*\n\n"
                "*Database:*\n"
                f"• Messages: {db_stats.get('messages_count', 0)}\n"
                f"• Contacts: {db_stats.get('contacts_count', 0)}\n"
                f"• Conversations: {db_stats.get('conversations_count', 0)}\n"
                f"• AI Responses: {db_stats.get('ai_responses_count', 0)}\n\n"
            )
            
            if wa_stats:
                if 'listener' in wa_stats:
                    listener_stats = wa_stats['listener']
                    stats_message += (
                        "*Message Processing:*\n"
                        f"• Total Processed: {listener_stats.get('processed_messages', 0)}\n"
                        f"• AI Responses Generated: {listener_stats.get('ai_responses_generated', 0)}\n"
                        f"• Processing Rate: {listener_stats.get('processing_rate', 0):.1%}\n\n"
                    )
                
                if 'sender' in wa_stats:
                    sender_stats = wa_stats['sender']
                    stats_message += (
                        "*Message Sending:*\n"
                        f"• Total Sent: {sender_stats.get('total_messages_sent', 0)}\n"
                        f"• AI Generated: {sender_stats.get('ai_generated_messages', 0)}\n"
                        f"• Manual: {sender_stats.get('manual_messages', 0)}\n\n"
                    )
                
                if 'uptime_seconds' in wa_stats:
                    uptime = wa_stats['uptime_seconds']
                    hours = int(uptime // 3600)
                    minutes = int((uptime % 3600) // 60)
                    stats_message += f"*Uptime:* {hours}h {minutes}m\n"
            
            await update.message.reply_text(
                stats_message,
                parse_mode=ParseMode.MARKDOWN
            )
            
        except Exception as e:
            logger.error(f"Error getting statistics: {e}")
            await update.message.reply_text("❌ Error retrieving statistics.")
    
    async def _handle_health(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle /health command."""
        if not self._check_authorization(update):
            await update.message.reply_text("❌ Unauthorized access.")
            return
        
        try:
            health_message = "🏥 *System Health Check*\n\n"
            
            # Database health
            db_healthy = self.db_manager.health_check()
            health_message += f"*Database:* {'🟢 Healthy' if db_healthy else '🔴 Issues'}\n"
            
            # WhatsApp health
            if self.whatsapp_manager:
                wa_health = await self.whatsapp_manager.health_check()
                health_icon = "🟢" if wa_health.get('healthy') else "🔴"
                health_message += f"*WhatsApp:* {health_icon} {'Healthy' if wa_health.get('healthy') else 'Issues'}\n"
                
                if wa_health.get('issues'):
                    health_message += "\n*Issues:*\n"
                    for issue in wa_health['issues']:
                        health_message += f"• {issue}\n"
            else:
                health_message += "*WhatsApp:* 🔴 Not initialized\n"
            
            health_message += f"\n*Check Time:* {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
            
            await update.message.reply_text(
                health_message,
                parse_mode=ParseMode.MARKDOWN
            )
            
        except Exception as e:
            logger.error(f"Error performing health check: {e}")
            await update.message.reply_text("❌ Error performing health check.")
    
    async def _handle_message(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle text messages."""
        if not self._check_authorization(update):
            return
        
        # For now, just acknowledge the message
        await update.message.reply_text(
            "👋 I received your message! Use /help to see available commands."
        )
    
    async def _handle_callback_query(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Handle inline keyboard callbacks."""
        if not self._check_authorization(update):
            await update.callback_query.answer("❌ Unauthorized access.")
            return
        
        query = update.callback_query
        data = query.data
        
        try:
            if data.startswith("confirm_"):
                await self._handle_message_confirmation(query, data)
            elif data.startswith("settings_"):
                await self._handle_settings_callback(query, data)
            elif data.startswith("contacts_"):
                await self._handle_contacts_callback(query, data)
            else:
                await query.answer("Unknown action.")
                
        except Exception as e:
            logger.error(f"Error handling callback query: {e}")
            await query.answer("❌ Error processing request.")
    
    async def _handle_message_confirmation(self, query, data: str):
        """Handle message confirmation callbacks."""
        try:
            # Parse callback data: confirm_action_messageId
            parts = data.split("_", 2)
            if len(parts) < 3:
                await query.answer("❌ Invalid confirmation data.")
                return

            action = parts[1]  # approve, edit, reject
            message_id = parts[2]

            # Get pending confirmation
            if message_id not in self.pending_confirmations:
                await query.answer("❌ Confirmation expired or not found.")
                return

            confirmation_data = self.pending_confirmations[message_id]

            if action == "approve":
                await self._approve_message(query, message_id, confirmation_data)
            elif action == "edit":
                await self._edit_message(query, message_id, confirmation_data)
            elif action == "reject":
                await self._reject_message(query, message_id, confirmation_data)
            else:
                await query.answer("❌ Unknown action.")

        except Exception as e:
            logger.error(f"Error handling message confirmation: {e}")
            await query.answer("❌ Error processing confirmation.")
    
    async def _handle_settings_callback(self, query, data: str):
        """Handle settings-related callbacks."""
        if data == "settings_refresh":
            await query.answer("Settings refreshed!")
            # Re-send settings message
            await self._handle_settings(query, None)
        else:
            await query.answer("Settings modification coming soon!")
    
    async def _handle_contacts_callback(self, query, data: str):
        """Handle contacts-related callbacks."""
        if data == "contacts_refresh":
            await query.answer("Contacts refreshed!")
            # Re-send contacts message
            await self._handle_contacts(query, None)
        else:
            await query.answer("Contact features coming soon!")
    
    async def start(self) -> bool:
        """Start the Telegram bot."""
        try:
            if not self.application:
                logger.error("Bot not initialized")
                return False
            
            logger.info("Starting Telegram bot...")
            
            # Start the bot
            await self.application.initialize()
            await self.application.start()
            
            # Start polling
            await self.application.updater.start_polling()
            
            self.is_running = True
            logger.info("Telegram bot started successfully")
            return True
            
        except Exception as e:
            logger.error(f"Failed to start Telegram bot: {e}")
            return False
    
    async def stop(self) -> bool:
        """Stop the Telegram bot."""
        try:
            if not self.is_running:
                return True
            
            logger.info("Stopping Telegram bot...")
            
            if self.application:
                await self.application.updater.stop()
                await self.application.stop()
                await self.application.shutdown()
            
            self.is_running = False
            logger.info("Telegram bot stopped")
            return True
            
        except Exception as e:
            logger.error(f"Error stopping Telegram bot: {e}")
            return False
    
    async def _approve_message(self, query, message_id: str, confirmation_data: Dict[str, Any]):
        """Approve and send the message."""
        try:
            chat_id = confirmation_data['chat_id']
            message_text = confirmation_data['message_text']
            contact_name = confirmation_data.get('contact_name', 'Unknown')

            # Send the message via WhatsApp
            if self.whatsapp_manager:
                success = await self.whatsapp_manager.send_message(chat_id, message_text)

                if success:
                    await query.answer("✅ Message sent!")
                    await query.edit_message_text(
                        f"✅ *Message Approved & Sent*\n\n"
                        f"*To:* {contact_name}\n"
                        f"*Message:* {message_text[:100]}{'...' if len(message_text) > 100 else ''}\n"
                        f"*Sent at:* {datetime.now().strftime('%H:%M:%S')}",
                        parse_mode=ParseMode.MARKDOWN
                    )

                    # Call callback if registered
                    if self.message_approved_callback:
                        await self.message_approved_callback(message_id, confirmation_data)
                else:
                    await query.answer("❌ Failed to send message!")
                    await query.edit_message_text(
                        f"❌ *Failed to Send Message*\n\n"
                        f"*To:* {contact_name}\n"
                        f"*Error:* Could not send via WhatsApp\n"
                        f"*Time:* {datetime.now().strftime('%H:%M:%S')}",
                        parse_mode=ParseMode.MARKDOWN
                    )
            else:
                await query.answer("❌ WhatsApp not available!")

            # Remove from pending confirmations
            self.pending_confirmations.pop(message_id, None)

        except Exception as e:
            logger.error(f"Error approving message: {e}")
            await query.answer("❌ Error sending message.")

    async def _edit_message(self, query, message_id: str, confirmation_data: Dict[str, Any]):
        """Allow editing the message before sending."""
        try:
            contact_name = confirmation_data.get('contact_name', 'Unknown')
            original_message = confirmation_data['message_text']

            edit_message = (
                f"✏️ *Edit Message for {contact_name}*\n\n"
                f"*Original:*\n{original_message}\n\n"
                f"Reply to this message with your edited version, or use the buttons below:"
            )

            keyboard = [
                [InlineKeyboardButton("✅ Send Original", callback_data=f"confirm_approve_{message_id}")],
                [InlineKeyboardButton("❌ Cancel", callback_data=f"confirm_reject_{message_id}")]
            ]
            reply_markup = InlineKeyboardMarkup(keyboard)

            await query.edit_message_text(
                edit_message,
                parse_mode=ParseMode.MARKDOWN,
                reply_markup=reply_markup
            )

            await query.answer("✏️ Edit mode activated!")

            # Mark as editing mode
            confirmation_data['editing'] = True
            self.pending_confirmations[message_id] = confirmation_data

        except Exception as e:
            logger.error(f"Error editing message: {e}")
            await query.answer("❌ Error entering edit mode.")

    async def _reject_message(self, query, message_id: str, confirmation_data: Dict[str, Any]):
        """Reject the message (don't send)."""
        try:
            contact_name = confirmation_data.get('contact_name', 'Unknown')
            message_text = confirmation_data['message_text']

            await query.answer("❌ Message rejected!")
            await query.edit_message_text(
                f"❌ *Message Rejected*\n\n"
                f"*To:* {contact_name}\n"
                f"*Message:* {message_text[:100]}{'...' if len(message_text) > 100 else ''}\n"
                f"*Rejected at:* {datetime.now().strftime('%H:%M:%S')}",
                parse_mode=ParseMode.MARKDOWN
            )

            # Call callback if registered
            if self.message_rejected_callback:
                await self.message_rejected_callback(message_id, confirmation_data)

            # Remove from pending confirmations
            self.pending_confirmations.pop(message_id, None)

        except Exception as e:
            logger.error(f"Error rejecting message: {e}")
            await query.answer("❌ Error rejecting message.")

    async def request_message_confirmation(self,
                                         chat_id: str,
                                         message_text: str,
                                         contact_name: str = "Unknown",
                                         confidence_score: Optional[float] = None,
                                         context: Optional[str] = None) -> str:
        """Request confirmation for a message before sending."""
        try:
            # Generate unique message ID
            message_id = f"msg_{int(datetime.now().timestamp())}_{len(self.pending_confirmations)}"

            # Store confirmation data
            confirmation_data = {
                'chat_id': chat_id,
                'message_text': message_text,
                'contact_name': contact_name,
                'confidence_score': confidence_score,
                'context': context,
                'created_at': datetime.now(),
                'editing': False
            }

            self.pending_confirmations[message_id] = confirmation_data

            # Create confirmation message
            preview_length = self.config.telegram.ui.message_preview_length
            message_preview = message_text[:preview_length]
            if len(message_text) > preview_length:
                message_preview += "..."

            confidence_text = ""
            if confidence_score is not None:
                confidence_icon = "🟢" if confidence_score > 0.8 else "🟡" if confidence_score > 0.6 else "🔴"
                confidence_text = f"*Confidence:* {confidence_icon} {confidence_score:.1%}\n"

            context_text = ""
            if context:
                context_text = f"*Context:* {context[:100]}{'...' if len(context) > 100 else ''}\n"

            confirmation_message = (
                f"🤖 *AI Generated Response*\n\n"
                f"*To:* {contact_name}\n"
                f"{confidence_text}"
                f"{context_text}"
                f"*Message:*\n{message_preview}\n\n"
                f"What would you like to do?"
            )

            # Create inline keyboard
            keyboard = [
                [
                    InlineKeyboardButton("✅ Approve", callback_data=f"confirm_approve_{message_id}"),
                    InlineKeyboardButton("✏️ Edit", callback_data=f"confirm_edit_{message_id}")
                ],
                [InlineKeyboardButton("❌ Reject", callback_data=f"confirm_reject_{message_id}")]
            ]
            reply_markup = InlineKeyboardMarkup(keyboard)

            # Send confirmation request
            await self.application.bot.send_message(
                chat_id=self.authorized_user_id,
                text=confirmation_message,
                parse_mode=ParseMode.MARKDOWN,
                reply_markup=reply_markup
            )

            logger.info(f"Message confirmation requested: {message_id}")

            # Schedule cleanup after timeout
            asyncio.create_task(self._cleanup_confirmation(message_id))

            return message_id

        except Exception as e:
            logger.error(f"Error requesting message confirmation: {e}")
            return None

    async def _cleanup_confirmation(self, message_id: str):
        """Clean up expired confirmation requests."""
        try:
            await asyncio.sleep(self.confirmation_timeout)

            if message_id in self.pending_confirmations:
                confirmation_data = self.pending_confirmations.pop(message_id)

                # Notify about timeout
                await self.application.bot.send_message(
                    chat_id=self.authorized_user_id,
                    text=f"⏰ *Confirmation Timeout*\n\n"
                         f"Message to {confirmation_data.get('contact_name', 'Unknown')} expired.",
                    parse_mode=ParseMode.MARKDOWN
                )

                logger.debug(f"Confirmation {message_id} expired and cleaned up")

        except Exception as e:
            logger.error(f"Error cleaning up confirmation {message_id}: {e}")

    def set_message_approved_callback(self, callback: Callable):
        """Set callback for message approval events."""
        self.message_approved_callback = callback

    def set_message_rejected_callback(self, callback: Callable):
        """Set callback for message rejection events."""
        self.message_rejected_callback = callback

    async def send_notification(self, message: str, urgent: bool = False):
        """Send a notification to the authorized user."""
        try:
            icon = "🚨" if urgent else "ℹ️"
            notification_text = f"{icon} *Notification*\n\n{message}"

            await self.application.bot.send_message(
                chat_id=self.authorized_user_id,
                text=notification_text,
                parse_mode=ParseMode.MARKDOWN
            )

            logger.debug(f"Notification sent: {message[:50]}...")

        except Exception as e:
            logger.error(f"Error sending notification: {e}")

    def get_status(self) -> Dict[str, Any]:
        """Get bot status."""
        return {
            'running': self.is_running,
            'authorized_user': self.authorized_user_id,
            'pending_confirmations': len(self.pending_confirmations)
        }
