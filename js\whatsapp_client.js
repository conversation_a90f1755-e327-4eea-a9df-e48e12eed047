/**
 * WhatsApp Web.js Client for WhatsApp AI Assistant
 * Handles WhatsApp Web integration and communicates with Python via IPC
 */

const { Client, LocalAuth, MessageMedia } = require('whatsapp-web.js');
const qrcode = require('qrcode-terminal');
const fs = require('fs');
const path = require('path');

class WhatsAppClient {
    constructor() {
        this.client = null;
        this.isReady = false;
        this.sessionPath = path.join(__dirname, '..', 'data', 'whatsapp_session');
        this.logFile = path.join(__dirname, '..', 'logs', 'whatsapp_js.log');
        
        // Ensure directories exist
        this.ensureDirectories();
        
        // Initialize client
        this.initializeClient();
        
        // Setup IPC communication
        this.setupIPC();
    }
    
    ensureDirectories() {
        const dirs = [
            path.dirname(this.sessionPath),
            path.dirname(this.logFile)
        ];
        
        dirs.forEach(dir => {
            if (!fs.existsSync(dir)) {
                fs.mkdirSync(dir, { recursive: true });
            }
        });
    }
    
    log(message, level = 'INFO') {
        const timestamp = new Date().toISOString();
        const logMessage = `${timestamp} [${level}] ${message}\n`;
        
        // Write to log file
        fs.appendFileSync(this.logFile, logMessage);
        
        // Also log to console
        console.log(`[${level}] ${message}`);
    }
    
    initializeClient() {
        this.client = new Client({
            authStrategy: new LocalAuth({
                clientId: "whatsapp-ai-assistant",
                dataPath: this.sessionPath
            }),
            puppeteer: {
                headless: true,
                args: [
                    '--no-sandbox',
                    '--disable-setuid-sandbox',
                    '--disable-dev-shm-usage',
                    '--disable-accelerated-2d-canvas',
                    '--no-first-run',
                    '--no-zygote',
                    '--single-process',
                    '--disable-gpu'
                ]
            }
        });
        
        this.setupEventHandlers();
    }
    
    setupEventHandlers() {
        // QR Code generation
        this.client.on('qr', (qr) => {
            this.log('QR Code received, scan with your phone');
            qrcode.generate(qr, { small: true });
            
            // Send QR to Python
            this.sendToPython({
                type: 'qr_code',
                data: qr
            });
        });
        
        // Authentication success
        this.client.on('authenticated', () => {
            this.log('Authentication successful');
            this.sendToPython({
                type: 'authenticated',
                data: true
            });
        });
        
        // Authentication failure
        this.client.on('auth_failure', (msg) => {
            this.log(`Authentication failed: ${msg}`, 'ERROR');
            this.sendToPython({
                type: 'auth_failure',
                data: msg
            });
        });
        
        // Client ready
        this.client.on('ready', () => {
            this.isReady = true;
            this.log('WhatsApp client is ready');
            this.sendToPython({
                type: 'ready',
                data: true
            });
        });
        
        // Incoming messages
        this.client.on('message', async (message) => {
            try {
                const messageData = await this.processMessage(message);
                this.sendToPython({
                    type: 'message',
                    data: messageData
                });
            } catch (error) {
                this.log(`Error processing message: ${error.message}`, 'ERROR');
            }
        });
        
        // Message acknowledgment
        this.client.on('message_ack', (message, ack) => {
            this.sendToPython({
                type: 'message_ack',
                data: {
                    id: message.id._serialized,
                    ack: ack
                }
            });
        });
        
        // Disconnection
        this.client.on('disconnected', (reason) => {
            this.log(`Client disconnected: ${reason}`, 'WARNING');
            this.isReady = false;
            this.sendToPython({
                type: 'disconnected',
                data: reason
            });
        });
        
        // Group join
        this.client.on('group_join', (notification) => {
            this.sendToPython({
                type: 'group_join',
                data: {
                    chatId: notification.chatId,
                    who: notification.who,
                    timestamp: notification.timestamp
                }
            });
        });
        
        // Group leave
        this.client.on('group_leave', (notification) => {
            this.sendToPython({
                type: 'group_leave',
                data: {
                    chatId: notification.chatId,
                    who: notification.who,
                    timestamp: notification.timestamp
                }
            });
        });
    }
    
    async processMessage(message) {
        const contact = await message.getContact();
        const chat = await message.getChat();
        
        return {
            id: message.id._serialized,
            body: message.body,
            type: message.type,
            timestamp: message.timestamp,
            from: message.from,
            to: message.to,
            isForwarded: message.isForwarded,
            isStatus: message.isStatus,
            isStarred: message.isStarred,
            broadcast: message.broadcast,
            fromMe: message.fromMe,
            hasMedia: message.hasMedia,
            hasQuotedMsg: message.hasQuotedMsg,
            location: message.location,
            mentionedIds: message.mentionedIds,
            contact: {
                id: contact.id._serialized,
                name: contact.name,
                pushname: contact.pushname,
                number: contact.number,
                isMyContact: contact.isMyContact,
                isUser: contact.isUser,
                isGroup: contact.isGroup,
                isWAContact: contact.isWAContact,
                profilePicUrl: await contact.getProfilePicUrl().catch(() => null)
            },
            chat: {
                id: chat.id._serialized,
                name: chat.name,
                isGroup: chat.isGroup,
                isReadOnly: chat.isReadOnly,
                unreadCount: chat.unreadCount,
                timestamp: chat.timestamp,
                archived: chat.archived,
                pinned: chat.pinned,
                isMuted: chat.isMuted
            }
        };
    }
    
    setupIPC() {
        // Listen for commands from Python via stdin
        process.stdin.setEncoding('utf8');
        process.stdin.on('data', (data) => {
            try {
                const commands = data.trim().split('\n');
                commands.forEach(commandStr => {
                    if (commandStr.trim()) {
                        const command = JSON.parse(commandStr);
                        this.handleCommand(command);
                    }
                });
            } catch (error) {
                this.log(`Error parsing command: ${error.message}`, 'ERROR');
            }
        });
        
        // Handle process termination
        process.on('SIGINT', () => {
            this.log('Received SIGINT, shutting down gracefully');
            this.shutdown();
        });
        
        process.on('SIGTERM', () => {
            this.log('Received SIGTERM, shutting down gracefully');
            this.shutdown();
        });
    }
    
    async handleCommand(command) {
        try {
            switch (command.type) {
                case 'send_message':
                    await this.sendMessage(command.data);
                    break;
                case 'get_chats':
                    await this.getChats();
                    break;
                case 'get_contacts':
                    await this.getContacts();
                    break;
                case 'get_chat_by_id':
                    await this.getChatById(command.data.chatId);
                    break;
                case 'mark_chat_unread':
                    await this.markChatUnread(command.data.chatId);
                    break;
                case 'archive_chat':
                    await this.archiveChat(command.data.chatId);
                    break;
                case 'pin_chat':
                    await this.pinChat(command.data.chatId);
                    break;
                case 'mute_chat':
                    await this.muteChat(command.data.chatId, command.data.duration);
                    break;
                case 'get_profile_pic':
                    await this.getProfilePic(command.data.contactId);
                    break;
                case 'set_status':
                    await this.setStatus(command.data.status);
                    break;
                case 'logout':
                    await this.logout();
                    break;
                case 'restart':
                    await this.restart();
                    break;
                default:
                    this.log(`Unknown command type: ${command.type}`, 'WARNING');
            }
        } catch (error) {
            this.log(`Error handling command ${command.type}: ${error.message}`, 'ERROR');
            this.sendToPython({
                type: 'error',
                data: {
                    command: command.type,
                    error: error.message
                }
            });
        }
    }
    
    async sendMessage(data) {
        if (!this.isReady) {
            throw new Error('Client is not ready');
        }
        
        const { chatId, message, options = {} } = data;
        
        try {
            const sentMessage = await this.client.sendMessage(chatId, message, options);
            
            this.sendToPython({
                type: 'message_sent',
                data: {
                    id: sentMessage.id._serialized,
                    chatId: chatId,
                    message: message,
                    timestamp: sentMessage.timestamp
                }
            });
        } catch (error) {
            throw new Error(`Failed to send message: ${error.message}`);
        }
    }
    
    async getChats() {
        if (!this.isReady) {
            throw new Error('Client is not ready');
        }
        
        const chats = await this.client.getChats();
        const chatData = chats.map(chat => ({
            id: chat.id._serialized,
            name: chat.name,
            isGroup: chat.isGroup,
            isReadOnly: chat.isReadOnly,
            unreadCount: chat.unreadCount,
            timestamp: chat.timestamp,
            archived: chat.archived,
            pinned: chat.pinned,
            isMuted: chat.isMuted
        }));
        
        this.sendToPython({
            type: 'chats_list',
            data: chatData
        });
    }
    
    async getContacts() {
        if (!this.isReady) {
            throw new Error('Client is not ready');
        }
        
        const contacts = await this.client.getContacts();
        const contactData = contacts.map(contact => ({
            id: contact.id._serialized,
            name: contact.name,
            pushname: contact.pushname,
            number: contact.number,
            isMyContact: contact.isMyContact,
            isUser: contact.isUser,
            isGroup: contact.isGroup,
            isWAContact: contact.isWAContact
        }));
        
        this.sendToPython({
            type: 'contacts_list',
            data: contactData
        });
    }
    
    sendToPython(data) {
        const message = JSON.stringify(data) + '\n';
        process.stdout.write(message);
    }
    
    async start() {
        this.log('Starting WhatsApp client...');
        try {
            await this.client.initialize();
        } catch (error) {
            this.log(`Failed to start client: ${error.message}`, 'ERROR');
            process.exit(1);
        }
    }
    
    async shutdown() {
        this.log('Shutting down WhatsApp client...');
        if (this.client) {
            await this.client.destroy();
        }
        process.exit(0);
    }
}

// Start the client
const whatsappClient = new WhatsAppClient();
whatsappClient.start();
