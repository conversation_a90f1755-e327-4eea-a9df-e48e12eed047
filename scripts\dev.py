#!/usr/bin/env python3
"""
Development helper script for WhatsApp AI Assistant.
Provides common development tasks and utilities.
"""

import os
import sys
import subprocess
import argparse
from pathlib import Path


class DevManager:
    """Development task manager."""
    
    def __init__(self):
        self.project_root = Path(__file__).parent.parent
        self.src_path = self.project_root / "src"
        
    def run_tests(self, verbose: bool = False, coverage: bool = False):
        """Run the test suite."""
        print("🧪 Running tests...")

        # Use sys.executable to ensure we use the current Python interpreter
        cmd = [sys.executable, "-m", "pytest"]
        
        if verbose:
            cmd.append("-v")
        
        if coverage:
            cmd.extend(["--cov=src", "--cov-report=html", "--cov-report=term"])
        
        cmd.append("tests/")
        
        try:
            subprocess.run(cmd, cwd=self.project_root, check=True)
            print("✅ Tests completed successfully")
        except subprocess.CalledProcessError:
            print("❌ Tests failed")
            sys.exit(1)
    
    def lint_code(self):
        """Run code linting."""
        print("🔍 Running code linting...")
        
        # Run flake8
        try:
            subprocess.run([sys.executable, "-m", "flake8", "src/", "tests/"],
                         cwd=self.project_root, check=True)
            print("✅ Flake8 linting passed")
        except subprocess.CalledProcessError:
            print("❌ Flake8 linting failed")

        # Run mypy
        try:
            subprocess.run([sys.executable, "-m", "mypy", "src/"],
                         cwd=self.project_root, check=True)
            print("✅ MyPy type checking passed")
        except subprocess.CalledProcessError:
            print("❌ MyPy type checking failed")
    
    def format_code(self):
        """Format code with Black."""
        print("🎨 Formatting code with Black...")
        
        try:
            subprocess.run([sys.executable, "-m", "black", "src/", "tests/", "scripts/"],
                         cwd=self.project_root, check=True)
            print("✅ Code formatting completed")
        except subprocess.CalledProcessError:
            print("❌ Code formatting failed")
    
    def check_dependencies(self):
        """Check for outdated dependencies."""
        print("📦 Checking dependencies...")
        
        try:
            result = subprocess.run(["pip", "list", "--outdated"], 
                                  capture_output=True, text=True)
            if result.stdout.strip():
                print("📋 Outdated packages:")
                print(result.stdout)
            else:
                print("✅ All packages are up to date")
        except subprocess.CalledProcessError:
            print("❌ Failed to check dependencies")
    
    def clean_cache(self):
        """Clean Python cache files."""
        print("🧹 Cleaning cache files...")
        
        import shutil
        
        # Remove __pycache__ directories
        for pycache in self.project_root.rglob("__pycache__"):
            if pycache.is_dir():
                shutil.rmtree(pycache)
                print(f"Removed {pycache}")
        
        # Remove .pyc files
        for pyc_file in self.project_root.rglob("*.pyc"):
            pyc_file.unlink()
            print(f"Removed {pyc_file}")
        
        # Remove pytest cache
        pytest_cache = self.project_root / ".pytest_cache"
        if pytest_cache.exists():
            shutil.rmtree(pytest_cache)
            print("Removed .pytest_cache")
        
        # Remove mypy cache
        mypy_cache = self.project_root / ".mypy_cache"
        if mypy_cache.exists():
            shutil.rmtree(mypy_cache)
            print("Removed .mypy_cache")
        
        print("✅ Cache cleanup completed")
    
    def start_dev_server(self):
        """Start the development server."""
        print("🚀 Starting development server...")
        
        # Set development environment
        env = os.environ.copy()
        env["DEBUG"] = "true"
        env["LOG_LEVEL"] = "DEBUG"
        
        try:
            subprocess.run([sys.executable, "src/main.py"],
                         cwd=self.project_root, env=env)
        except KeyboardInterrupt:
            print("\n🛑 Development server stopped")
    
    def create_migration(self, message: str):
        """Create a database migration."""
        print(f"📝 Creating migration: {message}")
        
        # This would integrate with Alembic for database migrations
        # For now, just a placeholder
        print("⚠️  Migration functionality not yet implemented")
        print("Migrations will be added in a future version")
    
    def backup_database(self):
        """Create a database backup."""
        print("💾 Creating database backup...")
        
        try:
            # Import here to avoid circular imports
            sys.path.insert(0, str(self.src_path))
            from data.database import get_database_manager
            
            db_manager = get_database_manager()
            success = db_manager.backup_database()
            
            if success:
                print("✅ Database backup created successfully")
            else:
                print("❌ Database backup failed")
                
        except Exception as e:
            print(f"❌ Database backup failed: {e}")
    
    def show_stats(self):
        """Show project statistics."""
        print("📊 Project Statistics")
        print("=" * 30)
        
        # Count Python files
        py_files = list(self.project_root.rglob("*.py"))
        print(f"Python files: {len(py_files)}")
        
        # Count lines of code
        total_lines = 0
        for py_file in py_files:
            try:
                with open(py_file, 'r', encoding='utf-8') as f:
                    total_lines += len(f.readlines())
            except:
                pass
        
        print(f"Total lines of code: {total_lines}")
        
        # Show directory structure
        print("\n📁 Project Structure:")
        for item in sorted(self.project_root.iterdir()):
            if item.is_dir() and not item.name.startswith('.'):
                print(f"  📁 {item.name}/")
                for subitem in sorted(item.iterdir()):
                    if subitem.is_file() and not subitem.name.startswith('.'):
                        print(f"    📄 {subitem.name}")


def main():
    """Main function."""
    parser = argparse.ArgumentParser(description="WhatsApp AI Assistant Development Tools")
    parser.add_argument("command", choices=[
        "test", "lint", "format", "deps", "clean", "dev", "migrate", "backup", "stats"
    ], help="Command to run")
    
    parser.add_argument("--verbose", "-v", action="store_true", help="Verbose output")
    parser.add_argument("--coverage", "-c", action="store_true", help="Run with coverage")
    parser.add_argument("--message", "-m", help="Migration message")
    
    args = parser.parse_args()
    
    dev_manager = DevManager()
    
    if args.command == "test":
        dev_manager.run_tests(verbose=args.verbose, coverage=args.coverage)
    elif args.command == "lint":
        dev_manager.lint_code()
    elif args.command == "format":
        dev_manager.format_code()
    elif args.command == "deps":
        dev_manager.check_dependencies()
    elif args.command == "clean":
        dev_manager.clean_cache()
    elif args.command == "dev":
        dev_manager.start_dev_server()
    elif args.command == "migrate":
        if not args.message:
            print("❌ Migration message is required. Use --message or -m")
            sys.exit(1)
        dev_manager.create_migration(args.message)
    elif args.command == "backup":
        dev_manager.backup_database()
    elif args.command == "stats":
        dev_manager.show_stats()


if __name__ == "__main__":
    main()
