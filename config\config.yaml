# WhatsApp AI Assistant Configuration

# Application Settings
app:
  name: "WhatsApp AI Assistant"
  version: "1.0.0"
  debug: false
  timezone: "Europe/Rome"

# AI Configuration
ai:
  primary_provider: "openai"
  fallback_providers: ["anthropic", "groq"]
  
  openai:
    model: "gpt-3.5-turbo"
    max_tokens: 1000
    temperature: 0.7
    top_p: 1.0
    frequency_penalty: 0.0
    presence_penalty: 0.0
  
  response:
    confidence_threshold: 0.8
    auto_send_threshold: 0.9
    max_context_length: 4000
    enable_streaming: true

# Memory and RAG Configuration
memory:
  short_term:
    max_messages: 50
    retention_hours: 24
  
  medium_term:
    max_conversations: 100
    retention_days: 30
  
  long_term:
    retention_days: 365
    enable_compression: true
  
  vector_store:
    collection_name: "whatsapp_memories"
    embedding_model: "all-MiniLM-L6-v2"
    similarity_threshold: 0.7
    max_results: 10

# Database Configuration
database:
  type: "sqlite"
  path: "data/whatsapp_assistant.db"
  enable_fts: true
  backup_interval_hours: 24
  max_backup_files: 7

# WhatsApp Integration
whatsapp:
  session_path: "data/whatsapp_session"
  headless: true
  devtools: false
  auto_reconnect: true
  reconnect_delay_seconds: 30
  max_reconnect_attempts: 5
  
  message_processing:
    ignore_own_messages: true
    ignore_groups: false
    ignore_broadcasts: true
    process_media: false

# Telegram Bot Configuration
telegram:
  webhook:
    enabled: false
    port: 8443
    path: "/webhook"
  
  ui:
    enable_inline_keyboards: true
    message_preview_length: 200
    auto_delete_notifications: true
    notification_timeout_seconds: 300

# Scheduling Configuration
scheduler:
  timezone: "Europe/Rome"
  persistence: true
  max_concurrent_jobs: 10
  
  default_intervals:
    cleanup_hours: 24
    backup_hours: 24
    pattern_analysis_hours: 168  # Weekly
    memory_consolidation_hours: 24

# Pattern Learning Configuration
patterns:
  enable_learning: true
  min_messages_for_analysis: 10
  update_interval_hours: 24
  
  linguistic:
    analyze_length: true
    analyze_emoji: true
    analyze_punctuation: true
    analyze_vocabulary: true
  
  temporal:
    analyze_activity_hours: true
    analyze_response_times: true
    analyze_weekly_patterns: true
  
  emotional:
    enable_sentiment_analysis: true
    track_mood_changes: true

# Contact Management
contacts:
  auto_categorization: true
  categories:
    - "family"
    - "friends"
    - "work"
    - "services"
    - "other"
  
  relationship_analysis:
    formality_detection: true
    frequency_tracking: true
    topic_extraction: true

# Security and Privacy
security:
  enable_encryption: true
  require_consent: true
  data_retention_days: 365
  
  privacy_levels:
    - "public"
    - "private"
    - "confidential"
  
  audit:
    enable_logging: true
    log_data_access: true
    log_modifications: true

# Logging Configuration
logging:
  level: "INFO"
  format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
  
  files:
    app: "logs/app.log"
    whatsapp: "logs/whatsapp.log"
    telegram: "logs/telegram.log"
    ai: "logs/ai.log"
    scheduler: "logs/scheduler.log"
  
  rotation:
    max_size: "10MB"
    backup_count: 5
    when: "midnight"

# Performance Configuration
performance:
  cache:
    enable_redis: true
    default_ttl_seconds: 3600
    max_memory_mb: 100
  
  rate_limiting:
    ai_requests_per_minute: 60
    telegram_messages_per_second: 30
    whatsapp_messages_per_minute: 100
  
  optimization:
    enable_async: true
    max_concurrent_tasks: 10
    batch_processing: true
