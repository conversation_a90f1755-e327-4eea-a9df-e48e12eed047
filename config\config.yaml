ai:
  analysis: {}
  auto_process_messages: true
  context: {}
  memory:
    context_window_messages: 10
    long_term_days: 30
    max_entries_per_contact: 100
    short_term_hours: 24
  openai:
    api_key: ********************************************************************************************************************************************************************
    embedding_model: text-embedding-ada-002
    max_tokens: 1000
    model: gpt-4o-mini
    temperature: 0.7
  primary_provider: openai
  require_confirmation: true
  response:
    max_length: 1000
    min_confidence_threshold: 0.7
    temperature: 0.7
  search: {}
chromadb:
  persist_directory: data/vector_db
database:
  url: sqlite:///data/whatsapp_assistant.db
logging: {}
telegram:
  bot_token: **********:AAEWdQFtPN8mvf2C-Yqwh8Ii6SLfsSi8dT0
  notification_timeout_seconds: 300
  user_id: '128335946'
whatsapp:
  auto_reconnect: true
  max_reconnect_attempts: 5
  reconnect_delay_seconds: 30
  session_path: data/whatsapp_sessions
