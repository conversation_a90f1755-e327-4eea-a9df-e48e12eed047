# WhatsApp AI Assistant

An intelligent WhatsApp assistant that learns your communication patterns and helps manage conversations through AI-powered responses and smart scheduling.

## 🚀 Features

- **AI-Powered Responses**: Contextual message generation using OpenAI API
- **Pattern Learning**: Learns your writing style and communication patterns
- **Smart Scheduling**: Intelligent message scheduling and reminders
- **RAG System**: Retrieval-Augmented Generation for context-aware responses
- **Telegram Control**: Full control interface through Telegram bot
- **Memory Management**: Short, medium, and long-term memory systems
- **Contact Profiling**: Automatic contact categorization and relationship analysis

## 🏗️ Architecture

- **WhatsApp Integration**: whatsapp-web.js
- **Telegram Bot**: python-telegram-bot
- **AI/LLM**: OpenAI API with fallback endpoints
- **Vector Database**: ChromaDB for embeddings
- **Database**: SQLite with FTS5 extensions
- **Scheduler**: APScheduler
- **Framework**: FastAPI + AsyncIO
- **Caching**: Redis for memory management

## 📁 Project Structure

```
whatsapp-ai-assistant/
├── src/
│   ├── core/              # Core AI and memory components
│   ├── integrations/      # WhatsApp, Telegram, OpenAI clients
│   ├── data/             # Database and vector store
│   ├── scheduling/       # Task scheduling and management
│   └── utils/            # Utilities and helpers
├── tests/                # Test suites
├── docs/                 # Documentation
├── config/               # Configuration files
├── data/                 # Data storage
├── logs/                 # Log files
└── requirements.txt      # Python dependencies
```

## 🚀 Quick Start

1. Clone the repository
2. Set up virtual environment: `python -m venv venv`
3. Activate environment: `source venv/bin/activate` (Linux/Mac) or `venv\Scripts\activate` (Windows)
4. Install dependencies: `pip install -r requirements.txt`
5. Configure environment variables in `.env`
6. Run the application: `python src/main.py`

## 📋 Development Status

This project is currently in active development. See the TODO document for detailed implementation roadmap.

## 🔒 Privacy & Security

- Local data storage with encryption
- Configurable data retention policies
- GDPR compliance features
- Contact consent management

## 📄 License

MIT License - see LICENSE file for details.
