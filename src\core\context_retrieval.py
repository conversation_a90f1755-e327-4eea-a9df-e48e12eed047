"""
Context Retrieval System for WhatsApp AI Assistant.
Implements RAG (Retrieval-Augmented Generation) for enhanced AI responses.
"""

import asyncio
from datetime import datetime, timedelta
from typing import Dict, Any, Optional, List, Tuple
import json
import re

from utils.logging import get_logger
from utils.config import get_config
from data.database import get_database_manager
from data.models import Contact, Message, Conversation, MemoryContext

from .vector_database import VectorDatabase
from .memory_manager import MemoryManager

logger = get_logger("context_retrieval")


class ContextRetrieval:
    """Retrieval-Augmented Generation system for context-aware AI responses."""
    
    def __init__(self, vector_db: VectorDatabase, memory_manager: MemoryManager):
        self.config = get_config()
        self.db_manager = get_database_manager()
        self.vector_db = vector_db
        self.memory_manager = memory_manager
        
        # Retrieval configuration
        self.max_context_length = self.config.ai.context.max_context_length
        self.similarity_threshold = self.config.ai.context.similarity_threshold
        self.context_relevance_weight = self.config.ai.context.relevance_weight
        self.temporal_decay_factor = self.config.ai.context.temporal_decay_factor
        
        # Context types and their weights
        self.context_weights = {
            'recent_messages': 1.0,
            'similar_messages': 0.8,
            'memory_context': 0.9,
            'conversation_summary': 0.7,
            'contact_profile': 0.6,
            'similar_conversations': 0.5
        }
        
        # State
        self.is_initialized = False
        
        logger.info("Context retrieval system initialized")
    
    async def initialize(self) -> bool:
        """Initialize the context retrieval system."""
        try:
            if not self.vector_db.is_initialized:
                logger.error("Vector database not initialized")
                return False
            
            if not self.memory_manager.is_initialized:
                logger.error("Memory manager not initialized")
                return False
            
            self.is_initialized = True
            logger.info("Context retrieval system initialized successfully")
            return True
            
        except Exception as e:
            logger.error(f"Failed to initialize context retrieval system: {e}")
            return False
    
    async def retrieve_context(self, 
                             contact_id: int, 
                             current_message: str,
                             chat_id: str = None,
                             context_types: Optional[List[str]] = None) -> Dict[str, Any]:
        """Retrieve comprehensive context for AI response generation."""
        try:
            if context_types is None:
                context_types = list(self.context_weights.keys())
            
            context = {
                'contact_id': contact_id,
                'current_message': current_message,
                'chat_id': chat_id,
                'retrieved_at': datetime.now().isoformat(),
                'context_score': 0.0,
                'components': {}
            }
            
            # Retrieve different types of context
            if 'recent_messages' in context_types:
                context['components']['recent_messages'] = await self._get_recent_messages_context(contact_id)
            
            if 'similar_messages' in context_types:
                context['components']['similar_messages'] = await self._get_similar_messages_context(
                    current_message, contact_id
                )
            
            if 'memory_context' in context_types:
                context['components']['memory_context'] = await self._get_memory_context(
                    current_message, contact_id
                )
            
            if 'conversation_summary' in context_types:
                context['components']['conversation_summary'] = await self._get_conversation_summary_context(contact_id)
            
            if 'contact_profile' in context_types:
                context['components']['contact_profile'] = await self._get_contact_profile_context(contact_id)
            
            if 'similar_conversations' in context_types:
                context['components']['similar_conversations'] = await self._get_similar_conversations_context(
                    current_message, contact_id
                )
            
            # Calculate overall context score
            context['context_score'] = self._calculate_context_score(context['components'])
            
            # Generate formatted context for AI
            context['formatted_context'] = await self._format_context_for_ai(context['components'])
            
            logger.debug(f"Retrieved context for contact {contact_id} with score {context['context_score']:.2f}")
            return context
            
        except Exception as e:
            logger.error(f"Error retrieving context: {e}")
            return {'error': str(e)}
    
    async def _get_recent_messages_context(self, contact_id: int) -> Dict[str, Any]:
        """Get recent messages context."""
        try:
            context = {
                'type': 'recent_messages',
                'messages': [],
                'relevance_score': 0.0,
                'temporal_score': 1.0  # Recent messages have highest temporal relevance
            }
            
            with self.db_manager.get_session() as session:
                recent_messages = session.query(Message).filter_by(
                    contact_id=contact_id
                ).order_by(Message.timestamp.desc()).limit(10).all()
                
                for msg in reversed(recent_messages):  # Chronological order
                    message_data = {
                        'text': msg.message_text,
                        'timestamp': msg.timestamp.isoformat(),
                        'is_from_me': msg.is_from_me,
                        'sender_name': msg.sender_name,
                        'message_type': msg.message_type
                    }
                    context['messages'].append(message_data)
                
                context['relevance_score'] = 1.0 if recent_messages else 0.0
            
            return context
            
        except Exception as e:
            logger.error(f"Error getting recent messages context: {e}")
            return {'type': 'recent_messages', 'error': str(e)}
    
    async def _get_similar_messages_context(self, query: str, contact_id: int) -> Dict[str, Any]:
        """Get similar messages context using vector search."""
        try:
            context = {
                'type': 'similar_messages',
                'messages': [],
                'relevance_score': 0.0,
                'similarity_threshold': self.similarity_threshold
            }
            
            # Search for similar messages
            similar_results = await self.vector_db.search_similar_messages(
                query=query,
                limit=5,
                filters={'contact_id': str(contact_id)}
            )
            
            total_relevance = 0.0
            for result in similar_results:
                similarity_score = 1.0 - result['distance']
                
                if similarity_score >= self.similarity_threshold:
                    message_data = {
                        'text': result['document'],
                        'similarity_score': similarity_score,
                        'metadata': result['metadata'],
                        'temporal_decay': self._calculate_temporal_decay(result['metadata'].get('timestamp'))
                    }
                    context['messages'].append(message_data)
                    total_relevance += similarity_score
            
            context['relevance_score'] = total_relevance / len(similar_results) if similar_results else 0.0
            
            return context
            
        except Exception as e:
            logger.error(f"Error getting similar messages context: {e}")
            return {'type': 'similar_messages', 'error': str(e)}
    
    async def _get_memory_context(self, query: str, contact_id: int) -> Dict[str, Any]:
        """Get memory context using vector search."""
        try:
            context = {
                'type': 'memory_context',
                'memories': [],
                'relevance_score': 0.0
            }
            
            # Search memory contexts
            memory_results = await self.vector_db.search_memory_context(
                query=query,
                limit=5
            )
            
            # Filter by contact and calculate relevance
            total_relevance = 0.0
            relevant_count = 0
            
            for result in memory_results:
                metadata = result['metadata']
                related_contacts = json.loads(metadata.get('related_contacts', '[]'))
                
                if str(contact_id) in related_contacts:
                    relevance_score = 1.0 - result['distance']
                    importance_weight = metadata.get('importance_level', 1) / 3.0  # Normalize to 0-1
                    
                    memory_data = {
                        'content': result['document'],
                        'relevance_score': relevance_score,
                        'importance_level': metadata.get('importance_level', 1),
                        'context_type': metadata.get('context_type', 'general'),
                        'weighted_score': relevance_score * importance_weight
                    }
                    context['memories'].append(memory_data)
                    total_relevance += memory_data['weighted_score']
                    relevant_count += 1
            
            context['relevance_score'] = total_relevance / relevant_count if relevant_count > 0 else 0.0
            
            return context
            
        except Exception as e:
            logger.error(f"Error getting memory context: {e}")
            return {'type': 'memory_context', 'error': str(e)}
    
    async def _get_conversation_summary_context(self, contact_id: int) -> Dict[str, Any]:
        """Get conversation summary context."""
        try:
            context = {
                'type': 'conversation_summary',
                'summary': None,
                'relevance_score': 0.0
            }
            
            # Get conversation context from memory manager
            memory_context = await self.memory_manager.get_conversation_context(
                contact_id=contact_id,
                current_message="",  # Empty for summary retrieval
                limit=0  # Don't need recent messages here
            )
            
            if memory_context.get('conversation_summary'):
                summary_data = memory_context['conversation_summary']
                context['summary'] = summary_data
                context['relevance_score'] = 0.8  # High relevance for summaries
            
            return context
            
        except Exception as e:
            logger.error(f"Error getting conversation summary context: {e}")
            return {'type': 'conversation_summary', 'error': str(e)}
    
    async def _get_contact_profile_context(self, contact_id: int) -> Dict[str, Any]:
        """Get contact profile context."""
        try:
            context = {
                'type': 'contact_profile',
                'profile': {},
                'relevance_score': 0.0
            }
            
            with self.db_manager.get_session() as session:
                contact = session.query(Contact).filter_by(id=contact_id).first()
                
                if contact:
                    context['profile'] = {
                        'name': contact.name,
                        'category': contact.category,
                        'relationship_score': contact.relationship_score,
                        'communication_frequency': contact.communication_frequency,
                        'preferences': contact.preferences,
                        'notes': contact.notes,
                        'last_seen': contact.last_seen.isoformat() if contact.last_seen else None
                    }
                    context['relevance_score'] = 0.7  # Moderate relevance for profile
                
                # Get memory contexts for this contact
                memory_contexts = session.query(MemoryContext).filter_by(
                    contact_id=contact_id
                ).order_by(MemoryContext.importance_level.desc()).limit(5).all()
                
                context['profile']['memory_contexts'] = [
                    {
                        'type': ctx.context_type,
                        'key': ctx.context_key,
                        'value': ctx.context_value,
                        'importance': ctx.importance_level,
                        'confidence': ctx.confidence_score
                    }
                    for ctx in memory_contexts
                ]
            
            return context
            
        except Exception as e:
            logger.error(f"Error getting contact profile context: {e}")
            return {'type': 'contact_profile', 'error': str(e)}
    
    async def _get_similar_conversations_context(self, query: str, contact_id: int) -> Dict[str, Any]:
        """Get similar conversations context."""
        try:
            context = {
                'type': 'similar_conversations',
                'conversations': [],
                'relevance_score': 0.0
            }
            
            # Search for similar conversations
            similar_results = await self.vector_db.search_similar_conversations(
                query=query,
                limit=3,
                filters={'contact_id': str(contact_id)}
            )
            
            total_relevance = 0.0
            for result in similar_results:
                similarity_score = 1.0 - result['distance']
                
                if similarity_score >= self.similarity_threshold:
                    conv_data = {
                        'summary': result['document'],
                        'similarity_score': similarity_score,
                        'metadata': result['metadata'],
                        'temporal_decay': self._calculate_temporal_decay(
                            result['metadata'].get('last_message_timestamp')
                        )
                    }
                    context['conversations'].append(conv_data)
                    total_relevance += similarity_score
            
            context['relevance_score'] = total_relevance / len(similar_results) if similar_results else 0.0
            
            return context
            
        except Exception as e:
            logger.error(f"Error getting similar conversations context: {e}")
            return {'type': 'similar_conversations', 'error': str(e)}
    
    def _calculate_temporal_decay(self, timestamp_str: Optional[str]) -> float:
        """Calculate temporal decay factor for context relevance."""
        try:
            if not timestamp_str:
                return 0.5  # Default decay for unknown timestamps
            
            timestamp = datetime.fromisoformat(timestamp_str.replace('Z', '+00:00'))
            time_diff = datetime.now() - timestamp.replace(tzinfo=None)
            
            # Exponential decay: more recent = higher relevance
            days_old = time_diff.total_seconds() / (24 * 3600)
            decay_factor = max(0.1, 1.0 * (self.temporal_decay_factor ** days_old))
            
            return decay_factor
            
        except Exception as e:
            logger.error(f"Error calculating temporal decay: {e}")
            return 0.5
    
    def _calculate_context_score(self, components: Dict[str, Any]) -> float:
        """Calculate overall context quality score."""
        try:
            total_score = 0.0
            total_weight = 0.0
            
            for component_type, component_data in components.items():
                if 'error' in component_data:
                    continue
                
                weight = self.context_weights.get(component_type, 0.5)
                relevance_score = component_data.get('relevance_score', 0.0)
                
                total_score += weight * relevance_score
                total_weight += weight
            
            return total_score / total_weight if total_weight > 0 else 0.0
            
        except Exception as e:
            logger.error(f"Error calculating context score: {e}")
            return 0.0
    
    async def _format_context_for_ai(self, components: Dict[str, Any]) -> str:
        """Format retrieved context for AI consumption."""
        try:
            formatted_parts = []
            
            # Recent messages
            if 'recent_messages' in components and components['recent_messages'].get('messages'):
                recent_msgs = components['recent_messages']['messages']
                if recent_msgs:
                    formatted_parts.append("## Recent Conversation:")
                    for msg in recent_msgs[-5:]:  # Last 5 messages
                        sender = "You" if msg['is_from_me'] else msg['sender_name']
                        formatted_parts.append(f"{sender}: {msg['text']}")
            
            # Memory context
            if 'memory_context' in components and components['memory_context'].get('memories'):
                memories = components['memory_context']['memories']
                if memories:
                    formatted_parts.append("\n## Relevant Context:")
                    for memory in memories[:3]:  # Top 3 memories
                        formatted_parts.append(f"- {memory['content']}")
            
            # Contact profile
            if 'contact_profile' in components and components['contact_profile'].get('profile'):
                profile = components['contact_profile']['profile']
                if profile.get('name'):
                    formatted_parts.append(f"\n## About {profile['name']}:")
                    if profile.get('category'):
                        formatted_parts.append(f"- Category: {profile['category']}")
                    if profile.get('memory_contexts'):
                        for ctx in profile['memory_contexts'][:2]:  # Top 2 contexts
                            formatted_parts.append(f"- {ctx['key']}: {ctx['value']}")
            
            # Conversation summary
            if 'conversation_summary' in components and components['conversation_summary'].get('summary'):
                summary = components['conversation_summary']['summary']
                formatted_parts.append(f"\n## Conversation Summary:")
                formatted_parts.append(summary['summary'])
                if summary.get('key_topics'):
                    formatted_parts.append(f"Key topics: {', '.join(summary['key_topics'])}")
            
            # Similar conversations
            if 'similar_conversations' in components and components['similar_conversations'].get('conversations'):
                similar_convs = components['similar_conversations']['conversations']
                if similar_convs:
                    formatted_parts.append("\n## Similar Past Conversations:")
                    for conv in similar_convs[:2]:  # Top 2 similar conversations
                        formatted_parts.append(f"- {conv['summary']}")
            
            formatted_context = "\n".join(formatted_parts)
            
            # Truncate if too long
            if len(formatted_context) > self.max_context_length:
                formatted_context = formatted_context[:self.max_context_length] + "..."
            
            return formatted_context
            
        except Exception as e:
            logger.error(f"Error formatting context for AI: {e}")
            return ""
    
    async def get_context_suggestions(self, contact_id: int, current_message: str) -> List[Dict[str, Any]]:
        """Get context-based suggestions for response generation."""
        try:
            suggestions = []
            
            # Get similar messages for response patterns
            similar_messages = await self.vector_db.search_similar_messages(
                query=current_message,
                limit=3,
                filters={'contact_id': str(contact_id), 'is_from_me': True}
            )
            
            for result in similar_messages:
                if 1.0 - result['distance'] > 0.7:  # High similarity
                    suggestions.append({
                        'type': 'similar_response',
                        'content': result['document'],
                        'confidence': 1.0 - result['distance'],
                        'source': 'previous_responses'
                    })
            
            # Get memory-based suggestions
            memory_results = await self.vector_db.search_memory_context(
                query=current_message,
                limit=2
            )
            
            for result in memory_results:
                metadata = result['metadata']
                if str(contact_id) in json.loads(metadata.get('related_contacts', '[]')):
                    suggestions.append({
                        'type': 'memory_context',
                        'content': result['document'],
                        'confidence': 1.0 - result['distance'],
                        'source': 'memory_context'
                    })
            
            # Sort by confidence
            suggestions.sort(key=lambda x: x['confidence'], reverse=True)
            
            return suggestions[:5]  # Top 5 suggestions
            
        except Exception as e:
            logger.error(f"Error getting context suggestions: {e}")
            return []
    
    def get_status(self) -> Dict[str, Any]:
        """Get context retrieval system status."""
        return {
            'initialized': self.is_initialized,
            'max_context_length': self.max_context_length,
            'similarity_threshold': self.similarity_threshold,
            'context_weights': self.context_weights,
            'temporal_decay_factor': self.temporal_decay_factor
        }
