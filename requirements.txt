# Core Framework
fastapi==0.104.1
uvicorn[standard]==0.24.0
pydantic==2.5.0
asyncio-mqtt==0.16.1

# AI and ML
openai==1.3.7
sentence-transformers==2.2.2
chromadb==0.4.18
numpy==1.24.3
pandas==2.0.3
scikit-learn==1.3.0
spacy==3.7.2

# WhatsApp Integration
# Note: whatsapp-web.js will be installed via npm

# Telegram Bot
python-telegram-bot==20.7

# Database
sqlite-fts4==1.0.3
sqlalchemy==2.0.23
alembic==1.12.1

# Caching and Memory
redis==5.0.1
hiredis==2.2.3

# Scheduling
apscheduler==3.10.4

# HTTP and Async
aiohttp==3.9.1
httpx==0.25.2
requests==2.31.0

# Utilities
python-dotenv==1.0.0
pyyaml==6.0.1
click==8.1.7
rich==13.7.0
loguru==0.7.2

# Security and Encryption
cryptography==41.0.8
bcrypt==4.1.2

# Data Processing
dateparser==1.2.0
pytz==2023.3
tzlocal==5.2

# Development and Testing
pytest==7.4.3
pytest-asyncio==0.21.1
pytest-cov==4.1.0
black==23.11.0
flake8==6.1.0
mypy==1.7.1

# Optional: For advanced NLP features
# transformers==4.35.2
# torch==2.1.1

# Optional: For voice processing
# speechrecognition==3.10.0
# pydub==0.25.1
