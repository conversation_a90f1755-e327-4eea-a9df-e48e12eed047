# Build tools (install first)
setuptools>=65.0.0
wheel>=0.38.0

# Core Framework
fastapi>=0.100.0
uvicorn[standard]>=0.20.0
pydantic>=2.0.0

# AI and ML
openai>=1.0.0
sentence-transformers>=2.2.0
chromadb>=0.4.0
numpy>=1.24.0
pandas>=2.0.0
scikit-learn>=1.3.0
tiktoken>=0.5.0

# Telegram Bot
python-telegram-bot>=20.0

# Database
sqlalchemy>=2.0.0
alembic>=1.12.0

# Caching and Memory
redis>=5.0.0

# Scheduling
apscheduler>=3.10.0

# HTTP and Async
aiohttp>=3.8.0
httpx>=0.24.0
requests>=2.31.0
websockets>=11.0.0

# WhatsApp Integration
qrcode>=7.4.2
pillow>=10.0.0

# Utilities
python-dotenv>=1.0.0
pyyaml>=6.0.0
click>=8.0.0
rich>=13.0.0
loguru>=0.7.0

# Security and Encryption
cryptography>=40.0.0

# Data Processing
dateparser>=1.1.0
pytz>=2023.3

# Development and Testing
pytest>=7.4.0
pytest-asyncio>=0.21.0
pytest-cov>=4.1.0
black>=23.0.0
flake8>=6.0.0
mypy>=1.5.0

# Optional: For advanced NLP features (uncomment if needed)
# transformers>=4.30.0
# torch>=2.0.0

# Optional: For voice processing (uncomment if needed)
# speechrecognition>=3.10.0
# pydub>=0.25.0
