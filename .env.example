# OpenAI Configuration
OPENAI_API_KEY=your_openai_api_key_here
OPENAI_MODEL=gpt-3.5-turbo
OPENAI_MAX_TOKENS=1000
OPENAI_TEMPERATURE=0.7

# Alternative AI Endpoints (optional)
ANTHROPIC_API_KEY=your_anthropic_key_here
GROQ_API_KEY=your_groq_key_here

# Telegram Bot Configuration
TELEGRAM_BOT_TOKEN=your_telegram_bot_token_here
TELEGRAM_USER_ID=your_telegram_user_id_here
TELEGRAM_WEBHOOK_URL=https://your-domain.com/webhook

# Database Configuration
DATABASE_URL=sqlite:///data/whatsapp_assistant.db
VECTOR_DB_PATH=data/chromadb

# Redis Configuration (for caching)
REDIS_URL=redis://localhost:6379/0
REDIS_PASSWORD=

# WhatsApp Configuration
WHATSAPP_SESSION_PATH=data/whatsapp_session
WHATSAPP_HEADLESS=true
WHATSAPP_DEVTOOLS=false

# Security
SECRET_KEY=your_secret_key_here
ENCRYPTION_KEY=your_encryption_key_here

# Logging
LOG_LEVEL=INFO
LOG_FILE=logs/app.log
LOG_MAX_SIZE=10MB
LOG_BACKUP_COUNT=5

# AI Behavior Configuration
CONFIDENCE_THRESHOLD=0.8
AUTO_SEND_THRESHOLD=0.9
MAX_CONTEXT_LENGTH=4000
MEMORY_RETENTION_DAYS=365

# Scheduling
SCHEDULER_TIMEZONE=Europe/Rome
SCHEDULER_PERSISTENCE=true

# Privacy and Security
DATA_RETENTION_DAYS=365
ENABLE_ENCRYPTION=true
REQUIRE_CONSENT=true

# Development
DEBUG=false
TESTING=false
