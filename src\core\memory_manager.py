"""
Memory Manager for WhatsApp AI Assistant.
Manages conversation memory, context, and long-term learning.
"""

import asyncio
from datetime import datetime, timedelta
from typing import Dict, Any, Optional, List, Tuple
import json
import hashlib

from utils.logging import get_logger
from utils.config import get_config
from data.database import get_database_manager
from data.models import Contact, Message, Conversation, MemoryContext, ConversationSummary

from .vector_database import VectorDatabase

logger = get_logger("memory_manager")


class MemoryManager:
    """Manages conversation memory and context for AI responses."""
    
    def __init__(self, vector_db: VectorDatabase):
        self.config = get_config()
        self.db_manager = get_database_manager()
        self.vector_db = vector_db
        
        # Memory configuration
        self.short_term_memory_hours = self.config.ai.memory.short_term_hours
        self.long_term_memory_days = self.config.ai.memory.long_term_days
        self.context_window_messages = self.config.ai.memory.context_window_messages
        self.max_memory_entries = self.config.ai.memory.max_entries_per_contact
        
        # Memory types
        self.memory_types = {
            'personal': 'Personal information and preferences',
            'behavioral': 'Communication patterns and behaviors',
            'contextual': 'Conversation context and topics',
            'emotional': 'Emotional state and sentiment patterns',
            'factual': 'Facts and information shared',
            'relational': 'Relationship dynamics and history'
        }
        
        # State
        self.is_initialized = False
        
        logger.info("Memory manager initialized")
    
    async def initialize(self) -> bool:
        """Initialize the memory manager."""
        try:
            if not self.vector_db.is_initialized:
                logger.error("Vector database not initialized")
                return False
            
            self.is_initialized = True
            logger.info("Memory manager initialized successfully")
            return True
            
        except Exception as e:
            logger.error(f"Failed to initialize memory manager: {e}")
            return False
    
    async def store_conversation_memory(self, 
                                      contact_id: int, 
                                      messages: List[Message],
                                      context_type: str = 'contextual') -> bool:
        """Store conversation memory from a list of messages."""
        try:
            if not messages:
                return True
            
            # Extract key information from messages
            memory_data = await self._extract_memory_from_messages(messages)
            
            if not memory_data:
                return True
            
            # Store in database
            with self.db_manager.get_session() as session:
                for memory_item in memory_data:
                    # Create memory context entry
                    memory_context = MemoryContext(
                        contact_id=contact_id,
                        context_type=context_type,
                        context_key=memory_item['key'],
                        context_value=memory_item['value'],
                        importance_level=memory_item.get('importance', 1),
                        confidence_score=memory_item.get('confidence', 0.5),
                        source_message_ids=json.dumps(memory_item.get('source_messages', [])),
                        metadata=memory_item.get('metadata', {}),
                        created_at=datetime.now()
                    )
                    
                    session.add(memory_context)
                
                session.commit()
            
            # Store embeddings in vector database
            for memory_item in memory_data:
                memory_text = f"{memory_item['key']}: {memory_item['value']}"
                memory_id = hashlib.md5(f"{contact_id}_{memory_item['key']}".encode()).hexdigest()
                
                await self.vector_db.add_memory_embedding(
                    memory_id=memory_id,
                    memory_text=memory_text,
                    metadata={
                        'context_type': context_type,
                        'context_key': memory_item['key'],
                        'importance_level': memory_item.get('importance', 1),
                        'relevance_score': memory_item.get('confidence', 0.5),
                        'related_contacts': [str(contact_id)],
                        'created_at': datetime.now().isoformat()
                    }
                )
            
            logger.debug(f"Stored {len(memory_data)} memory items for contact {contact_id}")
            return True
            
        except Exception as e:
            logger.error(f"Error storing conversation memory: {e}")
            return False
    
    async def _extract_memory_from_messages(self, messages: List[Message]) -> List[Dict[str, Any]]:
        """Extract memory items from messages using pattern matching and NLP."""
        try:
            memory_items = []
            
            # Combine all message texts
            conversation_text = "\n".join([msg.message_text for msg in messages if msg.message_text])
            
            if not conversation_text:
                return memory_items
            
            # Extract different types of memory
            memory_items.extend(await self._extract_personal_info(conversation_text, messages))
            memory_items.extend(await self._extract_behavioral_patterns(messages))
            memory_items.extend(await self._extract_topics_and_context(conversation_text, messages))
            memory_items.extend(await self._extract_emotional_context(conversation_text, messages))
            
            return memory_items
            
        except Exception as e:
            logger.error(f"Error extracting memory from messages: {e}")
            return []
    
    async def _extract_personal_info(self, text: str, messages: List[Message]) -> List[Dict[str, Any]]:
        """Extract personal information from conversation text."""
        memory_items = []
        
        try:
            # Simple pattern matching for personal information
            # In a production system, this would use more sophisticated NLP
            
            patterns = {
                'name_preference': r'call me (\w+)|my name is (\w+)|i\'m (\w+)',
                'location': r'i live in (\w+)|from (\w+)|in (\w+) city',
                'occupation': r'i work as|i\'m a (\w+)|my job is',
                'interests': r'i like|i love|i enjoy|interested in',
                'family': r'my wife|my husband|my son|my daughter|my family',
                'preferences': r'i prefer|i usually|i always|i never'
            }
            
            import re
            
            for pattern_name, pattern in patterns.items():
                matches = re.finditer(pattern, text.lower())
                for match in matches:
                    memory_items.append({
                        'key': pattern_name,
                        'value': match.group(0),
                        'importance': 2,
                        'confidence': 0.7,
                        'source_messages': [msg.id for msg in messages[-3:]],  # Last 3 messages
                        'metadata': {'extraction_method': 'pattern_matching'}
                    })
            
        except Exception as e:
            logger.error(f"Error extracting personal info: {e}")
        
        return memory_items
    
    async def _extract_behavioral_patterns(self, messages: List[Message]) -> List[Dict[str, Any]]:
        """Extract behavioral patterns from message history."""
        memory_items = []
        
        try:
            if len(messages) < 5:  # Need sufficient data for patterns
                return memory_items
            
            # Analyze communication patterns
            message_times = [msg.timestamp for msg in messages if msg.timestamp]
            message_lengths = [len(msg.message_text) for msg in messages if msg.message_text]
            
            if message_times:
                # Most active hours
                hours = [t.hour for t in message_times]
                most_active_hour = max(set(hours), key=hours.count) if hours else None
                
                if most_active_hour is not None:
                    memory_items.append({
                        'key': 'most_active_hour',
                        'value': str(most_active_hour),
                        'importance': 1,
                        'confidence': 0.6,
                        'source_messages': [msg.id for msg in messages[-5:]],
                        'metadata': {'pattern_type': 'temporal'}
                    })
            
            if message_lengths:
                # Average message length
                avg_length = sum(message_lengths) / len(message_lengths)
                length_category = 'short' if avg_length < 50 else 'medium' if avg_length < 150 else 'long'
                
                memory_items.append({
                    'key': 'message_length_preference',
                    'value': length_category,
                    'importance': 1,
                    'confidence': 0.5,
                    'source_messages': [msg.id for msg in messages[-10:]],
                    'metadata': {'pattern_type': 'communication_style', 'avg_length': avg_length}
                })
            
        except Exception as e:
            logger.error(f"Error extracting behavioral patterns: {e}")
        
        return memory_items
    
    async def _extract_topics_and_context(self, text: str, messages: List[Message]) -> List[Dict[str, Any]]:
        """Extract topics and contextual information."""
        memory_items = []
        
        try:
            # Simple keyword-based topic extraction
            # In production, this would use more sophisticated topic modeling
            
            topic_keywords = {
                'work': ['work', 'job', 'office', 'meeting', 'project', 'boss', 'colleague'],
                'family': ['family', 'wife', 'husband', 'son', 'daughter', 'parent', 'child'],
                'health': ['doctor', 'hospital', 'sick', 'medicine', 'health', 'pain'],
                'travel': ['travel', 'trip', 'vacation', 'flight', 'hotel', 'visit'],
                'food': ['food', 'restaurant', 'dinner', 'lunch', 'cooking', 'recipe'],
                'technology': ['computer', 'phone', 'app', 'software', 'internet', 'tech']
            }
            
            text_lower = text.lower()
            
            for topic, keywords in topic_keywords.items():
                keyword_count = sum(1 for keyword in keywords if keyword in text_lower)
                
                if keyword_count >= 2:  # Topic mentioned multiple times
                    memory_items.append({
                        'key': f'topic_interest_{topic}',
                        'value': f'Shows interest in {topic}',
                        'importance': 1,
                        'confidence': min(0.9, keyword_count * 0.2),
                        'source_messages': [msg.id for msg in messages[-5:]],
                        'metadata': {'topic': topic, 'keyword_count': keyword_count}
                    })
            
        except Exception as e:
            logger.error(f"Error extracting topics: {e}")
        
        return memory_items
    
    async def _extract_emotional_context(self, text: str, messages: List[Message]) -> List[Dict[str, Any]]:
        """Extract emotional context and sentiment patterns."""
        memory_items = []
        
        try:
            # Simple sentiment analysis based on keywords
            positive_words = ['happy', 'good', 'great', 'excellent', 'love', 'like', 'amazing', 'wonderful']
            negative_words = ['sad', 'bad', 'terrible', 'hate', 'angry', 'frustrated', 'disappointed']
            
            text_lower = text.lower()
            
            positive_count = sum(1 for word in positive_words if word in text_lower)
            negative_count = sum(1 for word in negative_words if word in text_lower)
            
            if positive_count > negative_count and positive_count > 0:
                sentiment = 'positive'
                confidence = min(0.8, positive_count * 0.2)
            elif negative_count > positive_count and negative_count > 0:
                sentiment = 'negative'
                confidence = min(0.8, negative_count * 0.2)
            else:
                sentiment = 'neutral'
                confidence = 0.3
            
            memory_items.append({
                'key': 'recent_sentiment',
                'value': sentiment,
                'importance': 1,
                'confidence': confidence,
                'source_messages': [msg.id for msg in messages[-3:]],
                'metadata': {
                    'positive_count': positive_count,
                    'negative_count': negative_count,
                    'analysis_method': 'keyword_based'
                }
            })
            
        except Exception as e:
            logger.error(f"Error extracting emotional context: {e}")
        
        return memory_items
    
    async def get_conversation_context(self, 
                                     contact_id: int, 
                                     current_message: str,
                                     limit: int = None) -> Dict[str, Any]:
        """Get relevant conversation context for AI response generation."""
        try:
            if limit is None:
                limit = self.context_window_messages
            
            context = {
                'recent_messages': [],
                'relevant_memory': [],
                'conversation_summary': None,
                'contact_profile': {},
                'similar_conversations': []
            }
            
            # Get recent messages
            with self.db_manager.get_session() as session:
                recent_messages = session.query(Message).filter_by(
                    contact_id=contact_id
                ).order_by(Message.timestamp.desc()).limit(limit).all()
                
                context['recent_messages'] = [
                    {
                        'text': msg.message_text,
                        'timestamp': msg.timestamp.isoformat(),
                        'is_from_me': msg.is_from_me,
                        'sender_name': msg.sender_name
                    }
                    for msg in reversed(recent_messages)  # Reverse to get chronological order
                ]
            
            # Get relevant memory from vector database
            memory_results = await self.vector_db.search_memory_context(
                query=current_message,
                limit=5
            )
            
            context['relevant_memory'] = [
                {
                    'content': result['document'],
                    'relevance': 1.0 - result['distance'],
                    'metadata': result['metadata']
                }
                for result in memory_results
            ]
            
            # Get conversation summary
            with self.db_manager.get_session() as session:
                summary = session.query(ConversationSummary).filter_by(
                    contact_id=contact_id
                ).order_by(ConversationSummary.created_at.desc()).first()
                
                if summary:
                    context['conversation_summary'] = {
                        'summary': summary.summary_text,
                        'key_topics': summary.key_topics,
                        'sentiment_trend': summary.sentiment_trend,
                        'last_updated': summary.created_at.isoformat()
                    }
            
            # Get contact profile
            context['contact_profile'] = await self._get_contact_profile(contact_id)
            
            # Get similar conversations
            similar_convs = await self.vector_db.search_similar_conversations(
                query=current_message,
                limit=3,
                filters={'contact_id': str(contact_id)}
            )
            
            context['similar_conversations'] = [
                {
                    'summary': result['document'],
                    'relevance': 1.0 - result['distance'],
                    'metadata': result['metadata']
                }
                for result in similar_convs
            ]
            
            logger.debug(f"Retrieved conversation context for contact {contact_id}")
            return context
            
        except Exception as e:
            logger.error(f"Error getting conversation context: {e}")
            return {}
    
    async def _get_contact_profile(self, contact_id: int) -> Dict[str, Any]:
        """Get contact profile information."""
        try:
            profile = {}
            
            with self.db_manager.get_session() as session:
                # Get basic contact info
                contact = session.query(Contact).filter_by(id=contact_id).first()
                if contact:
                    profile['name'] = contact.name
                    profile['category'] = contact.category
                    profile['relationship_score'] = contact.relationship_score
                    profile['communication_frequency'] = contact.communication_frequency
                    profile['last_seen'] = contact.last_seen.isoformat() if contact.last_seen else None
                
                # Get memory contexts
                memory_contexts = session.query(MemoryContext).filter_by(
                    contact_id=contact_id
                ).order_by(MemoryContext.importance_level.desc()).limit(10).all()
                
                profile['memory_contexts'] = [
                    {
                        'type': ctx.context_type,
                        'key': ctx.context_key,
                        'value': ctx.context_value,
                        'importance': ctx.importance_level,
                        'confidence': ctx.confidence_score
                    }
                    for ctx in memory_contexts
                ]
            
            return profile
            
        except Exception as e:
            logger.error(f"Error getting contact profile: {e}")
            return {}
    
    async def update_conversation_summary(self, contact_id: int) -> bool:
        """Update conversation summary for a contact."""
        try:
            with self.db_manager.get_session() as session:
                # Get recent messages for summarization
                recent_messages = session.query(Message).filter_by(
                    contact_id=contact_id
                ).order_by(Message.timestamp.desc()).limit(50).all()
                
                if not recent_messages:
                    return True
                
                # Create summary (simplified version)
                message_texts = [msg.message_text for msg in recent_messages if msg.message_text]
                
                if not message_texts:
                    return True
                
                # Simple summarization (in production, use AI summarization)
                summary_text = self._create_simple_summary(message_texts)
                key_topics = self._extract_key_topics(message_texts)
                sentiment_trend = self._analyze_sentiment_trend(message_texts)
                
                # Store or update summary
                existing_summary = session.query(ConversationSummary).filter_by(
                    contact_id=contact_id
                ).first()
                
                if existing_summary:
                    existing_summary.summary_text = summary_text
                    existing_summary.key_topics = key_topics
                    existing_summary.sentiment_trend = sentiment_trend
                    existing_summary.message_count = len(recent_messages)
                    existing_summary.updated_at = datetime.now()
                else:
                    new_summary = ConversationSummary(
                        contact_id=contact_id,
                        summary_text=summary_text,
                        key_topics=key_topics,
                        sentiment_trend=sentiment_trend,
                        message_count=len(recent_messages),
                        created_at=datetime.now()
                    )
                    session.add(new_summary)
                
                session.commit()
                
                # Store summary embedding
                summary_id = f"summary_{contact_id}"
                await self.vector_db.add_conversation_embedding(
                    conversation_id=summary_id,
                    summary=summary_text,
                    metadata={
                        'contact_id': contact_id,
                        'message_count': len(recent_messages),
                        'key_topics': key_topics,
                        'sentiment_trend': sentiment_trend,
                        'last_message_timestamp': recent_messages[0].timestamp.isoformat()
                    }
                )
                
                logger.debug(f"Updated conversation summary for contact {contact_id}")
                return True
                
        except Exception as e:
            logger.error(f"Error updating conversation summary: {e}")
            return False
    
    def _create_simple_summary(self, message_texts: List[str]) -> str:
        """Create a simple summary of messages."""
        # This is a placeholder - in production, use AI summarization
        total_messages = len(message_texts)
        avg_length = sum(len(text) for text in message_texts) / total_messages if total_messages > 0 else 0
        
        return f"Conversation with {total_messages} messages, average length {avg_length:.0f} characters."
    
    def _extract_key_topics(self, message_texts: List[str]) -> List[str]:
        """Extract key topics from messages."""
        # Simplified topic extraction
        all_text = " ".join(message_texts).lower()
        
        topics = []
        topic_keywords = {
            'work': ['work', 'job', 'office', 'meeting'],
            'family': ['family', 'wife', 'husband', 'son', 'daughter'],
            'health': ['doctor', 'hospital', 'sick', 'medicine'],
            'travel': ['travel', 'trip', 'vacation', 'flight']
        }
        
        for topic, keywords in topic_keywords.items():
            if any(keyword in all_text for keyword in keywords):
                topics.append(topic)
        
        return topics[:5]  # Return top 5 topics
    
    def _analyze_sentiment_trend(self, message_texts: List[str]) -> str:
        """Analyze sentiment trend in messages."""
        # Simplified sentiment analysis
        positive_words = ['good', 'great', 'happy', 'love', 'like']
        negative_words = ['bad', 'sad', 'angry', 'hate', 'terrible']
        
        all_text = " ".join(message_texts).lower()
        
        positive_count = sum(1 for word in positive_words if word in all_text)
        negative_count = sum(1 for word in negative_words if word in all_text)
        
        if positive_count > negative_count:
            return 'positive'
        elif negative_count > positive_count:
            return 'negative'
        else:
            return 'neutral'
    
    async def cleanup_old_memory(self, days_old: int = None) -> Dict[str, int]:
        """Clean up old memory entries."""
        try:
            if days_old is None:
                days_old = self.long_term_memory_days
            
            cutoff_date = datetime.now() - timedelta(days=days_old)
            cleanup_stats = {'database': 0, 'vector_db': 0}
            
            # Clean up database entries
            with self.db_manager.get_session() as session:
                old_memories = session.query(MemoryContext).filter(
                    MemoryContext.created_at < cutoff_date
                ).all()
                
                for memory in old_memories:
                    session.delete(memory)
                
                cleanup_stats['database'] = len(old_memories)
                session.commit()
            
            # Clean up vector database
            vector_cleanup = await self.vector_db.cleanup_old_embeddings(days_old)
            cleanup_stats['vector_db'] = sum(vector_cleanup.values())
            
            logger.info(f"Cleaned up {cleanup_stats['database']} database entries and {cleanup_stats['vector_db']} vector embeddings")
            return cleanup_stats
            
        except Exception as e:
            logger.error(f"Error cleaning up old memory: {e}")
            return {'database': 0, 'vector_db': 0}
    
    def get_status(self) -> Dict[str, Any]:
        """Get memory manager status."""
        return {
            'initialized': self.is_initialized,
            'short_term_hours': self.short_term_memory_hours,
            'long_term_days': self.long_term_memory_days,
            'context_window': self.context_window_messages,
            'max_entries_per_contact': self.max_memory_entries
        }
