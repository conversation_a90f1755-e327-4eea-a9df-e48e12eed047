"""
Vector Database Implementation for WhatsApp AI Assistant.
Manages ChromaDB integration for semantic search and embeddings.
"""

import asyncio
from datetime import datetime
from typing import Dict, Any, Optional, List, Union
from pathlib import Path
import json
import hashlib

import chromadb
from chromadb.config import Settings
from chromadb.utils import embedding_functions

from utils.logging import get_logger
from utils.config import get_config
from utils.helpers import FileHelper

logger = get_logger("vector_db")


class VectorDatabase:
    """Manages ChromaDB for semantic search and embeddings."""
    
    def __init__(self):
        self.config = get_config()
        
        # ChromaDB configuration
        self.db_path = Path(self.config.chromadb.persist_directory)
        self.client: Optional[chromadb.Client] = None
        
        # Collections
        self.collections: Dict[str, chromadb.Collection] = {}
        self.collection_configs = {
            'messages': {
                'name': 'whatsapp_messages',
                'metadata': {"description": "WhatsApp message embeddings"},
                'embedding_function': 'openai'
            },
            'conversations': {
                'name': 'conversation_summaries',
                'metadata': {"description": "Conversation summary embeddings"},
                'embedding_function': 'openai'
            },
            'contacts': {
                'name': 'contact_profiles',
                'metadata': {"description": "Contact profile embeddings"},
                'embedding_function': 'openai'
            },
            'memory': {
                'name': 'memory_context',
                'metadata': {"description": "Memory context embeddings"},
                'embedding_function': 'openai'
            },
            'patterns': {
                'name': 'user_patterns',
                'metadata': {"description": "User pattern embeddings"},
                'embedding_function': 'openai'
            }
        }
        
        # Embedding functions
        self.embedding_functions = {}
        
        # State
        self.is_initialized = False
        
        logger.info("Vector database initialized")
    
    async def initialize(self) -> bool:
        """Initialize ChromaDB and collections."""
        try:
            logger.info("Initializing ChromaDB...")
            
            # Ensure database directory exists
            FileHelper.ensure_directory(self.db_path)
            
            # Initialize ChromaDB client
            self.client = chromadb.PersistentClient(
                path=str(self.db_path),
                settings=Settings(
                    anonymized_telemetry=False,
                    allow_reset=True
                )
            )
            
            # Initialize embedding functions
            await self._initialize_embedding_functions()
            
            # Initialize collections
            await self._initialize_collections()
            
            self.is_initialized = True
            logger.info("ChromaDB initialized successfully")
            return True
            
        except Exception as e:
            logger.error(f"Failed to initialize ChromaDB: {e}")
            return False
    
    async def _initialize_embedding_functions(self):
        """Initialize embedding functions."""
        try:
            # OpenAI embedding function
            if self.config.ai.primary_provider == "openai" and self.config.ai.openai.api_key:
                self.embedding_functions['openai'] = embedding_functions.OpenAIEmbeddingFunction(
                    api_key=self.config.ai.openai.api_key,
                    model_name=self.config.ai.openai.embedding_model
                )
                logger.debug("OpenAI embedding function initialized")
            
            # Fallback to sentence transformers
            if 'openai' not in self.embedding_functions:
                self.embedding_functions['sentence_transformers'] = embedding_functions.SentenceTransformerEmbeddingFunction(
                    model_name="all-MiniLM-L6-v2"
                )
                logger.debug("SentenceTransformer embedding function initialized")
                
                # Update collection configs to use sentence transformers
                for config in self.collection_configs.values():
                    config['embedding_function'] = 'sentence_transformers'
            
        except Exception as e:
            logger.error(f"Error initializing embedding functions: {e}")
            # Use default embedding function as fallback
            self.embedding_functions['default'] = embedding_functions.DefaultEmbeddingFunction()
            for config in self.collection_configs.values():
                config['embedding_function'] = 'default'
    
    async def _initialize_collections(self):
        """Initialize all collections."""
        try:
            for collection_key, config in self.collection_configs.items():
                collection_name = config['name']
                embedding_func_name = config['embedding_function']
                embedding_func = self.embedding_functions.get(embedding_func_name)
                
                if not embedding_func:
                    logger.warning(f"Embedding function {embedding_func_name} not found, using default")
                    embedding_func = self.embedding_functions.get('default')
                
                try:
                    # Try to get existing collection
                    collection = self.client.get_collection(
                        name=collection_name,
                        embedding_function=embedding_func
                    )
                    logger.debug(f"Loaded existing collection: {collection_name}")
                    
                except Exception:
                    # Create new collection
                    collection = self.client.create_collection(
                        name=collection_name,
                        metadata=config['metadata'],
                        embedding_function=embedding_func
                    )
                    logger.debug(f"Created new collection: {collection_name}")
                
                self.collections[collection_key] = collection
            
            logger.info(f"Initialized {len(self.collections)} collections")
            
        except Exception as e:
            logger.error(f"Error initializing collections: {e}")
            raise
    
    async def add_message_embedding(self, 
                                  message_id: str, 
                                  text: str, 
                                  metadata: Dict[str, Any]) -> bool:
        """Add message embedding to the vector database."""
        try:
            if 'messages' not in self.collections:
                logger.error("Messages collection not initialized")
                return False
            
            collection = self.collections['messages']
            
            # Create document ID
            doc_id = f"msg_{message_id}"
            
            # Prepare metadata
            doc_metadata = {
                'message_id': message_id,
                'timestamp': metadata.get('timestamp', datetime.now().isoformat()),
                'contact_id': str(metadata.get('contact_id', '')),
                'chat_id': metadata.get('chat_id', ''),
                'sender_name': metadata.get('sender_name', ''),
                'is_from_me': metadata.get('is_from_me', False),
                'message_type': metadata.get('message_type', 'text'),
                'text_length': len(text)
            }
            
            # Add to collection
            collection.add(
                documents=[text],
                metadatas=[doc_metadata],
                ids=[doc_id]
            )
            
            logger.debug(f"Added message embedding: {doc_id}")
            return True
            
        except Exception as e:
            logger.error(f"Error adding message embedding: {e}")
            return False
    
    async def add_conversation_embedding(self, 
                                       conversation_id: str, 
                                       summary: str, 
                                       metadata: Dict[str, Any]) -> bool:
        """Add conversation summary embedding."""
        try:
            if 'conversations' not in self.collections:
                logger.error("Conversations collection not initialized")
                return False
            
            collection = self.collections['conversations']
            
            # Create document ID
            doc_id = f"conv_{conversation_id}"
            
            # Prepare metadata
            doc_metadata = {
                'conversation_id': conversation_id,
                'contact_id': str(metadata.get('contact_id', '')),
                'chat_id': metadata.get('chat_id', ''),
                'message_count': metadata.get('message_count', 0),
                'last_message_timestamp': metadata.get('last_message_timestamp', ''),
                'topic_tags': json.dumps(metadata.get('topic_tags', [])),
                'avg_sentiment': metadata.get('avg_sentiment', 0.0),
                'summary_length': len(summary)
            }
            
            # Add to collection
            collection.add(
                documents=[summary],
                metadatas=[doc_metadata],
                ids=[doc_id]
            )
            
            logger.debug(f"Added conversation embedding: {doc_id}")
            return True
            
        except Exception as e:
            logger.error(f"Error adding conversation embedding: {e}")
            return False
    
    async def add_contact_embedding(self, 
                                  contact_id: str, 
                                  profile_text: str, 
                                  metadata: Dict[str, Any]) -> bool:
        """Add contact profile embedding."""
        try:
            if 'contacts' not in self.collections:
                logger.error("Contacts collection not initialized")
                return False
            
            collection = self.collections['contacts']
            
            # Create document ID
            doc_id = f"contact_{contact_id}"
            
            # Prepare metadata
            doc_metadata = {
                'contact_id': contact_id,
                'phone_number': metadata.get('phone_number', ''),
                'name': metadata.get('name', ''),
                'category': metadata.get('category', 'other'),
                'relationship_score': metadata.get('relationship_score', 0.0),
                'communication_frequency': metadata.get('communication_frequency', 0.0),
                'last_interaction': metadata.get('last_interaction', ''),
                'profile_length': len(profile_text)
            }
            
            # Add to collection
            collection.add(
                documents=[profile_text],
                metadatas=[doc_metadata],
                ids=[doc_id]
            )
            
            logger.debug(f"Added contact embedding: {doc_id}")
            return True
            
        except Exception as e:
            logger.error(f"Error adding contact embedding: {e}")
            return False
    
    async def add_memory_embedding(self, 
                                 memory_id: str, 
                                 memory_text: str, 
                                 metadata: Dict[str, Any]) -> bool:
        """Add memory context embedding."""
        try:
            if 'memory' not in self.collections:
                logger.error("Memory collection not initialized")
                return False
            
            collection = self.collections['memory']
            
            # Create document ID
            doc_id = f"memory_{memory_id}"
            
            # Prepare metadata
            doc_metadata = {
                'memory_id': memory_id,
                'context_type': metadata.get('context_type', 'general'),
                'context_key': metadata.get('context_key', ''),
                'importance_level': metadata.get('importance_level', 1),
                'relevance_score': metadata.get('relevance_score', 0.5),
                'related_contacts': json.dumps(metadata.get('related_contacts', [])),
                'related_topics': json.dumps(metadata.get('related_topics', [])),
                'created_at': metadata.get('created_at', datetime.now().isoformat()),
                'memory_length': len(memory_text)
            }
            
            # Add to collection
            collection.add(
                documents=[memory_text],
                metadatas=[doc_metadata],
                ids=[doc_id]
            )
            
            logger.debug(f"Added memory embedding: {doc_id}")
            return True
            
        except Exception as e:
            logger.error(f"Error adding memory embedding: {e}")
            return False
    
    async def search_similar_messages(self, 
                                    query: str, 
                                    limit: int = 10,
                                    filters: Optional[Dict[str, Any]] = None) -> List[Dict[str, Any]]:
        """Search for similar messages."""
        try:
            if 'messages' not in self.collections:
                logger.error("Messages collection not initialized")
                return []
            
            collection = self.collections['messages']
            
            # Prepare where clause for filtering
            where_clause = {}
            if filters:
                for key, value in filters.items():
                    if key in ['contact_id', 'chat_id', 'sender_name', 'is_from_me', 'message_type']:
                        where_clause[key] = value
            
            # Perform search
            results = collection.query(
                query_texts=[query],
                n_results=limit,
                where=where_clause if where_clause else None
            )
            
            # Format results
            formatted_results = []
            if results['documents'] and results['documents'][0]:
                for i, doc in enumerate(results['documents'][0]):
                    result = {
                        'document': doc,
                        'metadata': results['metadatas'][0][i] if results['metadatas'] else {},
                        'distance': results['distances'][0][i] if results['distances'] else 0.0,
                        'id': results['ids'][0][i] if results['ids'] else ''
                    }
                    formatted_results.append(result)
            
            logger.debug(f"Found {len(formatted_results)} similar messages")
            return formatted_results
            
        except Exception as e:
            logger.error(f"Error searching similar messages: {e}")
            return []
    
    async def search_similar_conversations(self, 
                                         query: str, 
                                         limit: int = 5,
                                         filters: Optional[Dict[str, Any]] = None) -> List[Dict[str, Any]]:
        """Search for similar conversations."""
        try:
            if 'conversations' not in self.collections:
                logger.error("Conversations collection not initialized")
                return []
            
            collection = self.collections['conversations']
            
            # Prepare where clause
            where_clause = {}
            if filters:
                for key, value in filters.items():
                    if key in ['contact_id', 'chat_id']:
                        where_clause[key] = value
            
            # Perform search
            results = collection.query(
                query_texts=[query],
                n_results=limit,
                where=where_clause if where_clause else None
            )
            
            # Format results
            formatted_results = []
            if results['documents'] and results['documents'][0]:
                for i, doc in enumerate(results['documents'][0]):
                    result = {
                        'document': doc,
                        'metadata': results['metadatas'][0][i] if results['metadatas'] else {},
                        'distance': results['distances'][0][i] if results['distances'] else 0.0,
                        'id': results['ids'][0][i] if results['ids'] else ''
                    }
                    formatted_results.append(result)
            
            logger.debug(f"Found {len(formatted_results)} similar conversations")
            return formatted_results
            
        except Exception as e:
            logger.error(f"Error searching similar conversations: {e}")
            return []
    
    async def search_memory_context(self, 
                                  query: str, 
                                  context_type: Optional[str] = None,
                                  limit: int = 5) -> List[Dict[str, Any]]:
        """Search memory context for relevant information."""
        try:
            if 'memory' not in self.collections:
                logger.error("Memory collection not initialized")
                return []
            
            collection = self.collections['memory']
            
            # Prepare where clause
            where_clause = {}
            if context_type:
                where_clause['context_type'] = context_type
            
            # Perform search
            results = collection.query(
                query_texts=[query],
                n_results=limit,
                where=where_clause if where_clause else None
            )
            
            # Format results
            formatted_results = []
            if results['documents'] and results['documents'][0]:
                for i, doc in enumerate(results['documents'][0]):
                    result = {
                        'document': doc,
                        'metadata': results['metadatas'][0][i] if results['metadatas'] else {},
                        'distance': results['distances'][0][i] if results['distances'] else 0.0,
                        'id': results['ids'][0][i] if results['ids'] else ''
                    }
                    formatted_results.append(result)
            
            logger.debug(f"Found {len(formatted_results)} relevant memory contexts")
            return formatted_results
            
        except Exception as e:
            logger.error(f"Error searching memory context: {e}")
            return []
    
    async def update_embedding(self, collection_name: str, doc_id: str, text: str, metadata: Dict[str, Any]) -> bool:
        """Update an existing embedding."""
        try:
            if collection_name not in self.collections:
                logger.error(f"Collection {collection_name} not found")
                return False
            
            collection = self.collections[collection_name]
            
            # Update the document
            collection.update(
                ids=[doc_id],
                documents=[text],
                metadatas=[metadata]
            )
            
            logger.debug(f"Updated embedding: {doc_id} in {collection_name}")
            return True
            
        except Exception as e:
            logger.error(f"Error updating embedding: {e}")
            return False
    
    async def delete_embedding(self, collection_name: str, doc_id: str) -> bool:
        """Delete an embedding."""
        try:
            if collection_name not in self.collections:
                logger.error(f"Collection {collection_name} not found")
                return False
            
            collection = self.collections[collection_name]
            
            # Delete the document
            collection.delete(ids=[doc_id])
            
            logger.debug(f"Deleted embedding: {doc_id} from {collection_name}")
            return True
            
        except Exception as e:
            logger.error(f"Error deleting embedding: {e}")
            return False
    
    async def get_collection_stats(self) -> Dict[str, Any]:
        """Get statistics for all collections."""
        try:
            stats = {}
            
            for collection_key, collection in self.collections.items():
                count = collection.count()
                stats[collection_key] = {
                    'name': self.collection_configs[collection_key]['name'],
                    'count': count,
                    'embedding_function': self.collection_configs[collection_key]['embedding_function']
                }
            
            return stats
            
        except Exception as e:
            logger.error(f"Error getting collection stats: {e}")
            return {}
    
    async def cleanup_old_embeddings(self, days_old: int = 30) -> Dict[str, int]:
        """Clean up old embeddings based on age."""
        try:
            cleanup_stats = {}
            cutoff_date = datetime.now().timestamp() - (days_old * 24 * 60 * 60)
            
            for collection_key, collection in self.collections.items():
                deleted_count = 0
                
                # Get all documents with timestamps
                results = collection.get(include=['metadatas'])
                
                if results['metadatas']:
                    ids_to_delete = []
                    
                    for i, metadata in enumerate(results['metadatas']):
                        timestamp_str = metadata.get('timestamp') or metadata.get('created_at')
                        if timestamp_str:
                            try:
                                timestamp = datetime.fromisoformat(timestamp_str.replace('Z', '+00:00')).timestamp()
                                if timestamp < cutoff_date:
                                    ids_to_delete.append(results['ids'][i])
                            except:
                                continue
                    
                    if ids_to_delete:
                        collection.delete(ids=ids_to_delete)
                        deleted_count = len(ids_to_delete)
                
                cleanup_stats[collection_key] = deleted_count
                logger.debug(f"Cleaned up {deleted_count} old embeddings from {collection_key}")
            
            return cleanup_stats
            
        except Exception as e:
            logger.error(f"Error cleaning up old embeddings: {e}")
            return {}
    
    def get_status(self) -> Dict[str, Any]:
        """Get vector database status."""
        return {
            'initialized': self.is_initialized,
            'collections_count': len(self.collections),
            'embedding_functions': list(self.embedding_functions.keys()),
            'db_path': str(self.db_path)
        }
