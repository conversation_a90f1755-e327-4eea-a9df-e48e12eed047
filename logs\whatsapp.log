2025-06-22 23:05:02,647 - whatsapp - INFO - __init__:49 - WhatsApp manager initialized
2025-06-22 23:05:02,647 - whatsapp - INFO - initialize:58 - Initializing WhatsApp components...
2025-06-22 23:05:02,647 - whatsapp - INFO - __init__:44 - WhatsApp client initialized
2025-06-22 23:05:02,647 - whatsapp - INFO - __init__:55 - Authentication handler initialized
2025-06-22 23:05:02,648 - whatsapp - INFO - __init__:67 - Connection manager initialized
2025-06-22 23:05:02,648 - whatsapp - ERROR - initialize:85 - Failed to initialize WhatsApp manager: 'WhatsAppConfig' object has no attribute 'message_processing'
2025-06-22 23:05:02,671 - whatsapp - WARNING - stop:150 - WhatsApp manager not running
2025-06-22 23:11:56,980 - whatsapp - INFO - __init__:49 - What<PERSON><PERSON>pp manager initialized
2025-06-22 23:11:56,981 - whatsapp - INFO - initialize:58 - Initializing WhatsApp components...
2025-06-22 23:11:56,981 - whatsapp - INFO - __init__:44 - WhatsApp client initialized
2025-06-22 23:11:56,981 - whatsapp - INFO - __init__:55 - Authentication handler initialized
2025-06-22 23:11:56,982 - whatsapp - INFO - __init__:67 - Connection manager initialized
2025-06-22 23:11:56,982 - whatsapp - ERROR - initialize:85 - Failed to initialize WhatsApp manager: 'WhatsAppConfig' object has no attribute 'message_processing'
2025-06-22 23:11:57,001 - whatsapp - WARNING - stop:150 - WhatsApp manager not running
2025-06-22 23:12:58,695 - whatsapp - INFO - __init__:49 - WhatsApp manager initialized
2025-06-22 23:12:58,695 - whatsapp - INFO - initialize:58 - Initializing WhatsApp components...
2025-06-22 23:12:58,695 - whatsapp - INFO - __init__:44 - WhatsApp client initialized
2025-06-22 23:12:58,695 - whatsapp - INFO - __init__:55 - Authentication handler initialized
2025-06-22 23:12:58,696 - whatsapp - INFO - __init__:67 - Connection manager initialized
2025-06-22 23:12:58,696 - whatsapp - ERROR - initialize:85 - Failed to initialize WhatsApp manager: 'WhatsAppConfig' object has no attribute 'message_processing'
2025-06-22 23:12:58,715 - whatsapp - WARNING - stop:150 - WhatsApp manager not running
2025-06-22 23:15:23,174 - whatsapp - INFO - __init__:49 - WhatsApp manager initialized
2025-06-22 23:15:23,174 - whatsapp - INFO - initialize:58 - Initializing WhatsApp components...
2025-06-22 23:15:23,174 - whatsapp - INFO - __init__:44 - WhatsApp client initialized
2025-06-22 23:15:23,174 - whatsapp - INFO - __init__:55 - Authentication handler initialized
2025-06-22 23:15:23,175 - whatsapp - INFO - __init__:67 - Connection manager initialized
2025-06-22 23:15:23,175 - whatsapp - ERROR - initialize:85 - Failed to initialize WhatsApp manager: 'WhatsAppConfig' object has no attribute 'message_processing'
2025-06-22 23:15:23,194 - whatsapp - WARNING - stop:150 - WhatsApp manager not running
2025-06-22 23:16:05,302 - whatsapp - INFO - __init__:49 - WhatsApp manager initialized
2025-06-22 23:16:05,302 - whatsapp - INFO - initialize:58 - Initializing WhatsApp components...
2025-06-22 23:16:05,302 - whatsapp - INFO - __init__:44 - WhatsApp client initialized
2025-06-22 23:16:05,302 - whatsapp - INFO - __init__:55 - Authentication handler initialized
2025-06-22 23:16:05,303 - whatsapp - INFO - __init__:67 - Connection manager initialized
2025-06-22 23:16:05,303 - whatsapp - ERROR - initialize:86 - Failed to initialize WhatsApp manager: 'WhatsAppConfig' object has no attribute 'message_processing'
2025-06-22 23:16:05,307 - whatsapp - ERROR - initialize:87 - Traceback: Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\whastappAssistantV2\src\integrations\whatsapp_manager.py", line 70, in initialize
    self.message_listener = MessageListener(self.client)
                            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\whastappAssistantV2\src\integrations\message_listener.py", line 29, in __init__
    self.ignore_groups = self.config.whatsapp.message_processing.get('ignore_groups', False)
                         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Documents\whastappAssistantV2\venv\Lib\site-packages\pydantic\main.py", line 991, in __getattr__
    raise AttributeError(f'{type(self).__name__!r} object has no attribute {item!r}')
AttributeError: 'WhatsAppConfig' object has no attribute 'message_processing'

2025-06-22 23:16:05,327 - whatsapp - WARNING - stop:152 - WhatsApp manager not running
2025-06-22 23:17:21,203 - whatsapp - INFO - __init__:49 - WhatsApp manager initialized
2025-06-22 23:17:21,203 - whatsapp - INFO - initialize:58 - Initializing WhatsApp components...
2025-06-22 23:17:21,203 - whatsapp - INFO - __init__:44 - WhatsApp client initialized
2025-06-22 23:17:21,203 - whatsapp - INFO - __init__:55 - Authentication handler initialized
2025-06-22 23:17:21,203 - whatsapp - INFO - __init__:67 - Connection manager initialized
2025-06-22 23:17:21,203 - whatsapp - INFO - __init__:36 - Message listener initialized
2025-06-22 23:17:21,203 - whatsapp - INFO - __init__:38 - Message sender initialized
2025-06-22 23:17:21,222 - whatsapp - INFO - initialize:81 - WhatsApp components initialized successfully
2025-06-22 23:17:21,889 - whatsapp - WARNING - stop:152 - WhatsApp manager not running
2025-06-22 23:18:21,659 - whatsapp - INFO - __init__:49 - WhatsApp manager initialized
2025-06-22 23:18:21,660 - whatsapp - INFO - initialize:58 - Initializing WhatsApp components...
2025-06-22 23:18:21,660 - whatsapp - INFO - __init__:44 - WhatsApp client initialized
2025-06-22 23:18:21,660 - whatsapp - INFO - __init__:55 - Authentication handler initialized
2025-06-22 23:18:21,660 - whatsapp - INFO - __init__:67 - Connection manager initialized
2025-06-22 23:18:21,660 - whatsapp - INFO - __init__:36 - Message listener initialized
2025-06-22 23:18:21,660 - whatsapp - INFO - __init__:38 - Message sender initialized
2025-06-22 23:18:21,681 - whatsapp - INFO - initialize:81 - WhatsApp components initialized successfully
2025-06-22 23:18:22,326 - whatsapp - WARNING - stop:152 - WhatsApp manager not running
2025-06-22 23:19:54,398 - whatsapp - INFO - __init__:49 - WhatsApp manager initialized
2025-06-22 23:19:54,398 - whatsapp - INFO - initialize:58 - Initializing WhatsApp components...
2025-06-22 23:19:54,398 - whatsapp - INFO - __init__:44 - WhatsApp client initialized
2025-06-22 23:19:54,398 - whatsapp - INFO - __init__:55 - Authentication handler initialized
2025-06-22 23:19:54,398 - whatsapp - INFO - __init__:67 - Connection manager initialized
2025-06-22 23:19:54,398 - whatsapp - INFO - __init__:36 - Message listener initialized
2025-06-22 23:19:54,398 - whatsapp - INFO - __init__:38 - Message sender initialized
2025-06-22 23:19:54,418 - whatsapp - INFO - initialize:81 - WhatsApp components initialized successfully
2025-06-22 23:19:57,264 - whatsapp - WARNING - stop:152 - WhatsApp manager not running
2025-06-22 23:21:15,565 - whatsapp - INFO - __init__:49 - WhatsApp manager initialized
2025-06-22 23:21:15,565 - whatsapp - INFO - initialize:58 - Initializing WhatsApp components...
2025-06-22 23:21:15,565 - whatsapp - INFO - __init__:44 - WhatsApp client initialized
2025-06-22 23:21:15,565 - whatsapp - INFO - __init__:55 - Authentication handler initialized
2025-06-22 23:21:15,565 - whatsapp - INFO - __init__:67 - Connection manager initialized
2025-06-22 23:21:15,566 - whatsapp - INFO - __init__:36 - Message listener initialized
2025-06-22 23:21:15,566 - whatsapp - INFO - __init__:38 - Message sender initialized
2025-06-22 23:21:15,586 - whatsapp - INFO - initialize:81 - WhatsApp components initialized successfully
2025-06-22 23:21:18,023 - whatsapp - INFO - start:123 - Starting WhatsApp integration...
2025-06-22 23:21:18,023 - whatsapp - INFO - start_monitoring:88 - Connection monitoring started
2025-06-22 23:21:18,023 - whatsapp - INFO - start:65 - Starting WhatsApp Web.js client...
2025-06-22 23:21:18,027 - whatsapp - INFO - start:92 - WhatsApp client process started
2025-06-22 23:21:18,032 - whatsapp - INFO - start:136 - WhatsApp integration started successfully
2025-06-22 23:21:18,032 - whatsapp - INFO - _attempt_reconnection:177 - Attempting reconnection (attempt 1/5)
2025-06-22 23:21:18,033 - whatsapp - INFO - stop:277 - Stopping WhatsApp client...
2025-06-22 23:21:18,033 - whatsapp - WARNING - _send_command:189 - Cannot send command logout: client not running
2025-06-22 23:21:18,344 - whatsapp - WARNING - _read_output:109 - Failed to parse JSON from Node.js: [INFO] Starting WhatsApp client...
2025-06-22 23:21:23,047 - whatsapp - WARNING - stop:290 - Process didn't terminate gracefully, forcing...
2025-06-22 23:21:23,053 - whatsapp - INFO - stop:313 - WhatsApp client stopped
2025-06-22 23:21:25,072 - whatsapp - INFO - start:65 - Starting WhatsApp Web.js client...
2025-06-22 23:21:25,076 - whatsapp - INFO - start:92 - WhatsApp client process started
2025-06-22 23:21:25,076 - whatsapp - INFO - _attempt_reconnection:191 - Reconnection attempt initiated
2025-06-22 23:21:25,389 - whatsapp - WARNING - _read_output:109 - Failed to parse JSON from Node.js: [INFO] Starting WhatsApp client...
2025-06-22 23:21:29,484 - whatsapp - WARNING - _read_output:109 - Failed to parse JSON from Node.js: [INFO] QR Code received, scan with your phone
2025-06-22 23:21:29,504 - whatsapp - WARNING - _read_output:109 - Failed to parse JSON from Node.js: â–„â–„â–„â–„â–„â–„â–„â–„â–„â–„â–„â–„â–„â–„â–„â–„â–„â–„â–„â–„â–„â–„â–„â–„â–„â–„â–„â–„â–„â–„â–„â–„â–„â–„â–„â–„â–„â–„â–„â–„â–„â–„â–„â–„â–„â–„â–„â–„â–„â–„â–„â–„â–„â–„â–„â–„â–„â–„â–„
2025-06-22 23:21:29,504 - whatsapp - WARNING - _read_output:109 - Failed to parse JSON from Node.js: â–ˆ â–„â–„â–„â–„â–„ â–ˆâ–€  â–ˆâ–„â–„â–„   â–€  â–ˆ    â–„â–€â–„  â–„â–ˆâ–„â–„â–„â–„â–„â–„ â–„â–„ â–„â–ˆâ–ˆâ–„ â–ˆâ–ˆ â–„â–„â–„â–„â–„ â–ˆ
2025-06-22 23:21:29,504 - whatsapp - WARNING - _read_output:109 - Failed to parse JSON from Node.js: â–ˆ â–ˆ   â–ˆ â–ˆâ–„â–€ â–ˆâ–„â–„â–ˆâ–„â–ˆâ–ˆâ–ˆ â–ˆâ–„â–„â–„â–„â–ˆâ–ˆâ–ˆâ–„â–€â–„â–ˆâ–„â–€â–€â–ˆâ–ˆâ–€â–„â–ˆâ–„â–€â–„â–ˆâ–„â–€â–„ â–ˆâ–ˆ â–ˆ   â–ˆ â–ˆ
2025-06-22 23:21:29,504 - whatsapp - WARNING - _read_output:109 - Failed to parse JSON from Node.js: â–ˆ â–ˆâ–„â–„â–„â–ˆ â–ˆ  â–ˆ  â–€â–€â–ˆâ–ˆ  â–€â–ˆâ–€â–ˆâ–€â–„  â–„â–„â–„ â–ˆ â–ˆâ–€â–ˆâ–ˆâ–€ â–€ â–„ â–„  â–€â–„â–ˆâ–ˆ â–ˆâ–„â–„â–„â–ˆ â–ˆ
2025-06-22 23:21:29,504 - whatsapp - WARNING - _read_output:109 - Failed to parse JSON from Node.js: â–ˆâ–„â–„â–„â–„â–„â–„â–„â–ˆ â–ˆ â–ˆ â–€ â–€ â–ˆâ–„â–€ â–€â–„â–€â–„â–ˆ â–ˆâ–„â–ˆ â–€â–„â–ˆâ–„â–ˆ â–ˆâ–„â–ˆ â–€ â–ˆ â–ˆ â–ˆ â–ˆâ–„â–„â–„â–„â–„â–„â–„â–ˆ
2025-06-22 23:21:29,504 - whatsapp - WARNING - _read_output:109 - Failed to parse JSON from Node.js: â–ˆ â–„â–€â–„â–ˆâ–€â–„â–„â–ˆâ–„ â–„â–ˆâ–€â–€â–„â–ˆâ–€ â–ˆâ–ˆâ–„â–„â–„â–„â–„â–„â–„â–„â–„â–„â–ˆâ–ˆ â–„â–„ â–ˆâ–ˆâ–„â–„â–„â–ˆ â–€â–„â–„â–„â–„â–ˆâ–„â–„ â–€â–„â–„â–€â–ˆ
2025-06-22 23:21:29,504 - whatsapp - WARNING - _read_output:109 - Failed to parse JSON from Node.js: â–ˆ â–„â–„â–ˆâ–€â–„â–„ â–„ â–„â–„ â–€â–ˆ â–ˆâ–€â–„â–„â–„  â–€â–„  â–„â–ˆ â–€â–„â–„â–ˆâ–„ â–„ â–€â–ˆ â–ˆâ–ˆâ–„â–€â–„â–€â–ˆ  â–„â–ˆ â–€â–ˆâ–€ â–ˆ
2025-06-22 23:21:29,504 - whatsapp - WARNING - _read_output:109 - Failed to parse JSON from Node.js: â–ˆâ–€â–ˆâ–„â–„â–ˆ â–„  â–€â–€â–€â–ˆâ–„â–ˆâ–„ â–ˆâ–„â–ˆâ–ˆ â–€â–ˆâ–„ â–ˆ  â–ˆâ–€â–ˆâ–„â–ˆâ–€   â–ˆâ–„â–„â–„â–ˆâ–„â–„â–ˆ â–ˆâ–„â–€â–ˆâ–€ â–ˆâ–„  â–ˆ
2025-06-22 23:21:29,504 - whatsapp - WARNING - _read_output:109 - Failed to parse JSON from Node.js: â–ˆ â–€â–„â–„â–€â–€â–„â–ˆ â–€â–ˆâ–„â–ˆâ–ˆ  â–„â–€â–„â–€â–ˆâ–€â–ˆ â–€â–„â–„â–€â–ˆâ–„â–„ â–ˆâ–ˆâ–„ â–ˆâ–ˆâ–€â–€â–„â–„â–„â–ˆâ–„â–€â–€â–„â–ˆâ–€â–„â–ˆâ–€â–ˆâ–„â–„â–ˆâ–ˆ
2025-06-22 23:21:29,504 - whatsapp - WARNING - _read_output:109 - Failed to parse JSON from Node.js: â–ˆâ–„â–€â–€   â–„â–€ â–ˆ â–„â–€â–ˆ â–ˆâ–ˆâ–€ â–€â–€â–„â–ˆ â–„ â–ˆ   â–„â–ˆ  â–„â–„  â–€â–„â–€â–€â–„â–€â–„â–„â–ˆâ–€â–„â–„ â–€â–€â–„ â–ˆâ–„â–ˆ
2025-06-22 23:21:29,504 - whatsapp - WARNING - _read_output:109 - Failed to parse JSON from Node.js: â–ˆâ–„â–€â–„â–ˆ â–„â–„â–„ â–„â–€â–ˆ â–€â–„â–€ â–ˆâ–„â–€â–ˆâ–„  â–„â–€â–„ â–€â–„â–ˆâ–€ â–„â–€ â–ˆ â–ˆâ–ˆâ–„â–ˆâ–€â–€ â–„â–„â–„â–„â–ˆ â–„â–„â–„â–„â–„â–„â–ˆ
2025-06-22 23:21:29,504 - whatsapp - WARNING - _read_output:109 - Failed to parse JSON from Node.js: â–ˆâ–„â–ˆâ–ˆâ–„â–„â–„â–„â–€ â–„â–€â–ˆâ–€â–ˆâ–ˆâ–ˆ â–ˆâ–„ â–ˆ  â–„ â–ˆâ–€ â–„â–ˆâ–„â–„â–ˆâ–ˆâ–ˆâ–€ â–€â–ˆâ–„ â–ˆâ–ˆâ–ˆâ–€â–ˆâ–„â–€â–ˆâ–€â–ˆ  â–ˆâ–ˆâ–„â–€â–ˆ
2025-06-22 23:21:29,504 - whatsapp - WARNING - _read_output:109 - Failed to parse JSON from Node.js: â–ˆ â–€â–„â–„â–ˆ â–„â–„ â–„â–„â–„ â–ˆâ–ˆâ–ˆ â–ˆ â–€ â–€â–„â–€â–€â–„â–ˆâ–ˆâ–„â–ˆâ–ˆâ–€â–€â–ˆâ–€  â–€â–€ â–ˆâ–€ â–€â–€ â–€â–„â–„â–ˆâ–ˆâ–€â–„ â–ˆâ–„â–„â–ˆ
2025-06-22 23:21:29,504 - whatsapp - WARNING - _read_output:109 - Failed to parse JSON from Node.js: â–ˆâ–ˆâ–„â–„â–€  â–„ â–€â–ˆ â–€ â–€â–„â–„   â–ˆâ–ˆ â–„â–„â–„â–€â–„â–„ â–„â–ˆâ–„â–€â–€ â–„ â–„â–„â–„â–„â–ˆâ–ˆâ–„ â–„  â–ˆâ–ˆâ–ˆ â–„ â–„â–„â–ˆâ–ˆ
2025-06-22 23:21:29,504 - whatsapp - WARNING - _read_output:109 - Failed to parse JSON from Node.js: â–ˆâ–ˆâ–„â–ˆâ–€ â–„â–„â–„  â–ˆâ–ˆâ–ˆâ–ˆâ–ˆâ–€â–€â–€â–€â–„â–€â–ˆâ–„â–€â–ˆâ–„ â–„â–„â–„   â–„â–ˆâ–ˆ â–„â–„â–€â–ˆâ–„â–„ â–ˆâ–„ â–„ â–„â–„â–„  â–„ â–€â–ˆ
2025-06-22 23:21:29,504 - whatsapp - WARNING - _read_output:109 - Failed to parse JSON from Node.js: â–ˆ â–„â–„â–„ â–ˆâ–„â–ˆ â–„ â–€â–ˆâ–€ â–„â–„â–„â–ˆâ–€â–€â–ˆâ–ˆ â–ˆ  â–ˆâ–„â–ˆ â–ˆâ–„â–ˆâ–„â–„ â–ˆâ–€â–„â–€â–€â–„â–€ â–€â–„  â–ˆâ–„â–ˆ  â–ˆâ–„â–„â–ˆ
2025-06-22 23:21:29,506 - whatsapp - WARNING - _read_output:109 - Failed to parse JSON from Node.js: â–ˆâ–ˆâ–ˆâ–ˆâ–€â–„ â–„  â–„â–ˆâ–„ â–„â–„â–€â–„â–„â–„â–„â–„â–€â–€ â–ˆ  â–„ â–„â–„â–„â–€â–„â–„ â–„ â–„â–ˆâ–„â–„â–„â–€ â–„â–€      â–„â–„  â–ˆ
2025-06-22 23:21:29,506 - whatsapp - WARNING - _read_output:109 - Failed to parse JSON from Node.js: â–ˆ â–€  â–€â–„â–„â–„â–ˆâ–ˆ â–„â–€â–€â–€â–„â–„â–„  â–„  â–ˆâ–„â–€â–„â–„â–ˆâ–„â–„ â–ˆ â–„â–ˆâ–„â–ˆâ–„ â–„â–„â–„â–€â–ˆâ–ˆâ–„â–„â–„   â–ˆâ–ˆâ–ˆâ–ˆâ–€â–ˆ
2025-06-22 23:21:29,506 - whatsapp - WARNING - _read_output:109 - Failed to parse JSON from Node.js: â–ˆ â–ˆâ–ˆ  â–€â–„â–€â–„â–€â–ˆâ–ˆâ–€ â–ˆ â–ˆâ–ˆâ–„  â–ˆâ–ˆâ–ˆâ–ˆâ–„â–€â–€â–ˆâ–€â–„â–€â–€â–€â–„â–ˆâ–ˆ   â–€â–ˆ â–€â–„â–„â–„â–„ â–„â–„ â–„â–€â–ˆâ–„ â–ˆ
2025-06-22 23:21:29,506 - whatsapp - WARNING - _read_output:109 - Failed to parse JSON from Node.js: â–ˆ    â–€â–€â–„ â–„â–„â–„â–€â–ˆâ–€â–€    â–€ â–ˆâ–„ â–„â–„â–ˆ  â–€â–ˆ  â–ˆâ–€   â–„â–„ â–„â–„â–€ â–ˆ â–€â–€â–ˆ â–„â–€â–ˆâ–„â–„ â–ˆ
2025-06-22 23:21:29,506 - whatsapp - WARNING - _read_output:109 - Failed to parse JSON from Node.js: â–ˆâ–€â–€â–„â–€â–„ â–„â–ˆâ–€â–„â–€  â–„  â–„  â–ˆ â–ˆâ–ˆâ–€â–„â–€  â–„â–„ â–„â–„â–€â–„â–„â–„â–„â–ˆ â–€â–„ â–€â–ˆ â–ˆ â–€ â–€â–„â–€â–„â–„ â–„â–ˆ
2025-06-22 23:21:29,506 - whatsapp - WARNING - _read_output:109 - Failed to parse JSON from Node.js: â–ˆ â–ˆ â–€  â–„â–ˆ â–ˆâ–„ â–€ â–„â–ˆâ–ˆâ–„ â–€â–€ â–ˆ â–„ â–„â–ˆâ–ˆâ–„â–ˆâ–„  â–ˆâ–„â–„â–€ â–ˆâ–€â–„â–„â–ˆâ–„â–ˆâ–„â–€  â–€â–ˆâ–„â–ˆâ–„â–„â–„â–ˆ
2025-06-22 23:21:29,506 - whatsapp - WARNING - _read_output:109 - Failed to parse JSON from Node.js: â–ˆâ–„â–ˆâ–€  â–„â–„â–€â–„â–ˆâ–€â–ˆâ–€â–€ â–ˆ â–ˆâ–„ â–ˆ â–ˆ  â–ˆ â–ˆâ–ˆ â–„â–ˆ â–€ â–„â–„â–ˆâ–ˆâ–„â–„ â–ˆâ–€â–„â–€ â–€ â–„ â–€â–ˆ â–ˆ  â–ˆ
2025-06-22 23:21:29,506 - whatsapp - WARNING - _read_output:109 - Failed to parse JSON from Node.js: â–ˆ â–„   â–ˆâ–„ â–ˆâ–ˆ â–„â–„â–ˆâ–ˆâ–„â–„â–„â–„â–ˆâ–„ â–ˆâ–„â–„â–€â–€â–„â–„  â–€â–€â–„â–ˆâ–€â–„â–ˆâ–ˆâ–€ â–ˆâ–ˆ â–ˆâ–„â–„ â–ˆâ–€â–„ â–ˆâ–ˆâ–ˆâ–€â–€â–ˆ
2025-06-22 23:21:29,506 - whatsapp - WARNING - _read_output:109 - Failed to parse JSON from Node.js: â–ˆ â–€ â–€â–€â–„â–„â–€â–ˆ â–„â–€â–„â–„â–„â–ˆâ–„â–ˆâ–ˆâ–€â–ˆ â–€ â–ˆâ–„â–€â–€â–€â–„â–„ â–„â–ˆâ–€â–€â–€â–„ â–„â–€â–€â–„â–„â–ˆ â–€â–€â–€â–€  â–ˆ â–ˆâ–€ â–ˆ
2025-06-22 23:21:29,506 - whatsapp - WARNING - _read_output:109 - Failed to parse JSON from Node.js: â–ˆâ–ˆâ–ˆâ–ˆâ–ˆâ–ˆâ–ˆâ–„â–„â–€â–ˆâ–€â–ˆâ–„â–€ â–ˆâ–€â–„ â–„â–ˆâ–ˆâ–„ â–ˆâ–€ â–„â–„â–„   â–ˆ â–„ â–€â–„â–ˆ â–€â–ˆ  â–€â–€â–€ â–„â–„â–„  â–ˆâ–ˆâ–ˆâ–ˆ
2025-06-22 23:21:29,506 - whatsapp - WARNING - _read_output:109 - Failed to parse JSON from Node.js: â–ˆ â–„â–„â–„â–„â–„ â–ˆâ–„ â–€â–€â–„â–„â–ˆ â–„â–€â–€â–€â–„  â–ˆ â–€ â–ˆâ–„â–ˆ â–€ â–€ â–€â–€â–€â–ˆâ–€â–„â–ˆâ–€â–„ â–€â–ˆâ–„ â–ˆâ–„â–ˆ â–„â–ˆ â–€â–ˆ
2025-06-22 23:21:29,506 - whatsapp - WARNING - _read_output:109 - Failed to parse JSON from Node.js: â–ˆ â–ˆ   â–ˆ â–ˆâ–€â–„    â–ˆâ–ˆâ–€  â–€  â–€â–€ â–€â–„â–„    â–„ â–„ â–„â–„ â–„â–€â–€â–„â–„ â–„â–ˆâ–„   â–„â–„â–€â–€â–€ â–ˆ
2025-06-22 23:21:29,506 - whatsapp - WARNING - _read_output:109 - Failed to parse JSON from Node.js: â–ˆ â–ˆâ–„â–„â–„â–ˆ â–ˆâ–€â–„â–„â–ˆâ–„ â–€â–ˆâ–ˆâ–€â–€â–„â–„â–ˆâ–„â–ˆâ–€â–€â–„â–„â–„â–ˆâ–€â–„â–„â–„â–ˆâ–€â–ˆâ–„â–€ â–ˆâ–€â–€â–ˆ â–ˆ   â–€â–ˆâ–„ â–ˆâ–ˆâ–ˆâ–„â–ˆ
2025-06-22 23:21:29,506 - whatsapp - WARNING - _read_output:109 - Failed to parse JSON from Node.js: â–ˆâ–„â–„â–„â–„â–„â–„â–„â–ˆâ–„â–„â–ˆâ–„â–ˆâ–„â–ˆâ–ˆâ–„â–ˆâ–„â–ˆâ–„â–„â–„â–ˆâ–ˆâ–„â–ˆâ–„â–„â–„â–„â–„â–„â–ˆâ–ˆâ–ˆâ–„â–„â–„â–„â–„â–„â–„â–„â–ˆâ–„â–„â–„â–„â–„â–ˆâ–ˆâ–„â–„â–„â–„â–ˆâ–ˆ
2025-06-22 23:21:29,506 - whatsapp - WARNING - _read_output:109 - Failed to parse JSON from Node.js: 
2025-06-22 23:21:29,506 - whatsapp - INFO - _handle_event:160 - QR code received for WhatsApp authentication
2025-06-22 23:21:29,506 - whatsapp - ERROR - _handle_event:177 - Error in event handler for qr_code: no running event loop
2025-06-22 23:21:29,521 - whatsapp - ERROR - _handle_event:177 - Error in event handler for qr_code: no running event loop
2025-06-22 23:22:10,845 - whatsapp - WARNING - _read_output:109 - Failed to parse JSON from Node.js: [INFO] Received SIGINT, shutting down gracefully
2025-06-22 23:22:10,845 - whatsapp - WARNING - _read_output:109 - Failed to parse JSON from Node.js: [INFO] Shutting down WhatsApp client...
2025-06-22 23:22:12,077 - whatsapp - INFO - stop:155 - Stopping WhatsApp integration...
2025-06-22 23:22:12,080 - whatsapp - INFO - stop_monitoring:102 - Connection monitoring stopped
2025-06-22 23:22:12,080 - whatsapp - INFO - stop:277 - Stopping WhatsApp client...
2025-06-22 23:22:12,080 - whatsapp - WARNING - _send_command:189 - Cannot send command logout: client not running
2025-06-22 23:22:12,080 - whatsapp - INFO - stop:313 - WhatsApp client stopped
2025-06-22 23:22:12,084 - whatsapp - INFO - stop:168 - WhatsApp integration stopped
2025-06-22 23:22:20,333 - whatsapp - INFO - __init__:49 - WhatsApp manager initialized
2025-06-22 23:22:20,333 - whatsapp - INFO - initialize:58 - Initializing WhatsApp components...
2025-06-22 23:22:20,333 - whatsapp - INFO - __init__:44 - WhatsApp client initialized
2025-06-22 23:22:20,334 - whatsapp - INFO - __init__:55 - Authentication handler initialized
2025-06-22 23:22:20,334 - whatsapp - INFO - __init__:67 - Connection manager initialized
2025-06-22 23:22:20,334 - whatsapp - INFO - __init__:36 - Message listener initialized
2025-06-22 23:22:20,334 - whatsapp - INFO - __init__:38 - Message sender initialized
2025-06-22 23:22:20,354 - whatsapp - INFO - initialize:81 - WhatsApp components initialized successfully
2025-06-22 23:22:22,693 - whatsapp - INFO - start:123 - Starting WhatsApp integration...
2025-06-22 23:22:22,694 - whatsapp - INFO - start_monitoring:88 - Connection monitoring started
2025-06-22 23:22:22,694 - whatsapp - INFO - start:65 - Starting WhatsApp Web.js client...
2025-06-22 23:22:22,698 - whatsapp - INFO - start:92 - WhatsApp client process started
2025-06-22 23:22:22,703 - whatsapp - INFO - start:136 - WhatsApp integration started successfully
2025-06-22 23:22:22,703 - whatsapp - INFO - _attempt_reconnection:177 - Attempting reconnection (attempt 1/5)
2025-06-22 23:22:22,703 - whatsapp - INFO - stop:277 - Stopping WhatsApp client...
2025-06-22 23:22:22,703 - whatsapp - WARNING - _send_command:189 - Cannot send command logout: client not running
2025-06-22 23:22:23,011 - whatsapp - WARNING - _read_output:109 - Failed to parse JSON from Node.js: [INFO] Starting WhatsApp client...
2025-06-22 23:22:27,717 - whatsapp - WARNING - stop:290 - Process didn't terminate gracefully, forcing...
2025-06-22 23:22:27,723 - whatsapp - INFO - stop:313 - WhatsApp client stopped
2025-06-22 23:22:29,737 - whatsapp - INFO - start:65 - Starting WhatsApp Web.js client...
2025-06-22 23:22:29,740 - whatsapp - INFO - start:92 - WhatsApp client process started
2025-06-22 23:22:29,740 - whatsapp - INFO - _attempt_reconnection:191 - Reconnection attempt initiated
2025-06-22 23:22:30,045 - whatsapp - WARNING - _read_output:109 - Failed to parse JSON from Node.js: [INFO] Starting WhatsApp client...
2025-06-22 23:22:33,304 - whatsapp - WARNING - _read_output:109 - Failed to parse JSON from Node.js: [INFO] QR Code received, scan with your phone
2025-06-22 23:22:33,321 - whatsapp - WARNING - _read_output:109 - Failed to parse JSON from Node.js: â–„â–„â–„â–„â–„â–„â–„â–„â–„â–„â–„â–„â–„â–„â–„â–„â–„â–„â–„â–„â–„â–„â–„â–„â–„â–„â–„â–„â–„â–„â–„â–„â–„â–„â–„â–„â–„â–„â–„â–„â–„â–„â–„â–„â–„â–„â–„â–„â–„â–„â–„â–„â–„â–„â–„â–„â–„â–„â–„
2025-06-22 23:22:33,321 - whatsapp - WARNING - _read_output:109 - Failed to parse JSON from Node.js: â–ˆ â–„â–„â–„â–„â–„ â–ˆâ–„ â–€â–„â–ˆâ–„â–€â–€â–ˆ â–€â–„â–ˆâ–ˆ â–„ â–„â–€â–„â–ˆ â–„â–ˆâ–€â–ˆâ–ˆ â–„â–„â–ˆâ–ˆâ–ˆâ–€ â–ˆâ–€ â–„ â–ˆâ–ˆ â–„â–„â–„â–„â–„ â–ˆ
2025-06-22 23:22:33,321 - whatsapp - WARNING - _read_output:109 - Failed to parse JSON from Node.js: â–ˆ â–ˆ   â–ˆ â–ˆ  â–ˆâ–€â–ˆâ–€â–„â–ˆâ–„â–€â–€â–„â–„â–ˆâ–ˆâ–€â–€ â–ˆâ–ˆâ–„ â–„â–€â–ˆâ–ˆ â–€  â–ˆâ–„â–„ â–„â–€â–„â–€â–„ â–ˆâ–ˆ â–ˆ   â–ˆ â–ˆ
2025-06-22 23:22:33,321 - whatsapp - WARNING - _read_output:109 - Failed to parse JSON from Node.js: â–ˆ â–ˆâ–„â–„â–„â–ˆ â–ˆâ–„â–€â–€â–ˆâ–€â–„â–ˆâ–€â–€â–„â–„ â–€â–ˆ  â–ˆâ–€ â–„â–„â–„ â–€  â–€ â–ˆâ–ˆ â–€â–€â–€â–€â–„â–„â–„â–€â–„â–ˆâ–ˆ â–ˆâ–„â–„â–„â–ˆ â–ˆ
2025-06-22 23:22:33,321 - whatsapp - WARNING - _read_output:109 - Failed to parse JSON from Node.js: â–ˆâ–„â–„â–„â–„â–„â–„â–„â–ˆâ–„â–ˆ â–ˆâ–„â–ˆâ–„â–ˆâ–„â–€â–„â–€â–„â–€ â–€â–„â–ˆ â–ˆâ–„â–ˆ â–€ â–€ â–ˆ â–€ â–€â–„â–ˆâ–„â–ˆ â–ˆ â–ˆ â–ˆâ–„â–„â–„â–„â–„â–„â–„â–ˆ
2025-06-22 23:22:33,321 - whatsapp - WARNING - _read_output:109 - Failed to parse JSON from Node.js: â–ˆ  â–„ â–€â–ˆâ–„â–€  â–„â–„â–„â–€â–ˆâ–ˆâ–ˆâ–„â–€â–€â–„â–„  â–€â–€â–„     â–ˆ â–„â–€â–ˆâ–„â–„â–„â–„â–€ â–ˆâ–ˆâ–„â–„â–€  â–ˆâ–ˆâ–„â–„ â–ˆ â–ˆ
2025-06-22 23:22:33,322 - whatsapp - WARNING - _read_output:109 - Failed to parse JSON from Node.js: â–ˆâ–„â–€â–ˆâ–ˆâ–ˆ â–„â–„ â–„â–€â–€â–ˆ â–€ â–„ â–ˆâ–ˆ â–„â–„ â–„â–ˆâ–ˆâ–„â–€  â–€â–„â–€â–„â–€â–„ â–€ â–ˆ â–„â–€â–€â–€â–ˆâ–„â–€â–€ â–„â–ˆ â–„â–„ â–ˆ
2025-06-22 23:22:33,322 - whatsapp - WARNING - _read_output:109 - Failed to parse JSON from Node.js: â–ˆ   â–ˆâ–ˆ â–„ â–€â–„â–ˆâ–ˆ â–„  â–„â–ˆâ–„â–€ â–ˆâ–„â–„â–„ â–ˆâ–€â–ˆâ–ˆâ–€â–„â–ˆâ–€â–„ â–„ â–€ â–€â–€â–„â–€â–€â–ˆâ–„â–ˆâ–„ â–€â–€â–ˆâ–€ â–€â–„â–ˆ
2025-06-22 23:22:33,322 - whatsapp - WARNING - _read_output:109 - Failed to parse JSON from Node.js: â–ˆ    â–€â–ˆâ–„ â–€â–€â–€â–ˆâ–„ â–ˆâ–„â–€ â–ˆâ–„â–„â–„â–„â–€â–€ â–„â–€â–ˆâ–ˆâ–ˆ â–ˆâ–ˆ  â–ˆâ–„â–€â–€  â–„â–€ â–„â–€â–„â–ˆ â–ˆâ–ˆ  â–€ â–ˆâ–ˆ
2025-06-22 23:22:33,322 - whatsapp - WARNING - _read_output:109 - Failed to parse JSON from Node.js: â–ˆâ–ˆâ–„â–„ â–€â–„â–„â–ˆâ–„â–„ â–ˆâ–„ â–„â–€â–€â–ˆâ–ˆâ–„â–ˆ â–€â–€â–„â–„ â–„â–„â–€â–ˆâ–„â–„â–„â–€â–ˆâ–€â–ˆâ–„â–„â–€â–€â–„â–„ â–ˆâ–€ â–ˆâ–„â–„â–€â–ˆâ–ˆ â–ˆâ–ˆâ–ˆ
2025-06-22 23:22:33,322 - whatsapp - WARNING - _read_output:109 - Failed to parse JSON from Node.js: â–ˆâ–ˆâ–„    â–„â–„ â–€â–ˆâ–ˆâ–€â–€â–€â–€â–„â–„â–ˆ â–€â–„ â–€â–„ â–€â–€ â–ˆâ–€  â–ˆâ–„â–ˆâ–€â–„â–„â–„â–€â–ˆâ–ˆ  â–€ â–€ â–„â–€â–ˆâ–ˆâ–„â–„ â–€â–ˆ
2025-06-22 23:22:33,322 - whatsapp - WARNING - _read_output:109 - Failed to parse JSON from Node.js: â–ˆâ–„â–„â–ˆâ–ˆâ–„ â–„â–„â–ˆâ–ˆâ–€â–„â–„ â–ˆâ–ˆ â–€â–ˆâ–ˆâ–ˆâ–ˆ â–€â–ˆ  â–€â–„â–€â–€â–€â–€â–€â–ˆâ–ˆâ–€â–€â–ˆ â–„â–€ â–ˆâ–ˆâ–€â–„â–„  â–„ â–„â–€â–€â–€ â–ˆ
2025-06-22 23:22:33,322 - whatsapp - WARNING - _read_output:109 - Failed to parse JSON from Node.js: â–ˆâ–„â–€â–„â–„ â–€â–„â–„â–ˆ  â–„  â–ˆâ–€â–„â–€â–„ â–„ â–ˆ  â–ˆâ–€â–ˆâ–„â–„â–ˆâ–€â–ˆ â–„â–ˆâ–€ â–ˆ â–ˆ â–€â–ˆ â–„ â–„â–€ â–€â–€ â–ˆâ–„â–ˆ â–ˆ
2025-06-22 23:22:33,322 - whatsapp - WARNING - _read_output:109 - Failed to parse JSON from Node.js: â–ˆ â–ˆ  â–€â–ˆâ–„â–€â–„â–„â–„â–€â–ˆâ–ˆ â–„â–„â–„â–„â–ˆ â–€â–€â–ˆâ–ˆâ–ˆâ–ˆâ–ˆâ–ˆâ–„â–ˆâ–„â–„â–„â–ˆ    â–„â–€â–„â–ˆâ–€â–€â–ˆâ–ˆâ–ˆâ–ˆ  â–ˆâ–ˆâ–€â–ˆâ–„ â–ˆ
2025-06-22 23:22:33,322 - whatsapp - WARNING - _read_output:109 - Failed to parse JSON from Node.js: â–ˆâ–ˆâ–„â–ˆâ–€ â–„â–„â–„ â–€â–„â–ˆâ–„â–ˆâ–„â–ˆâ–ˆâ–„â–„â–€â–€â–ˆâ–„â–ˆâ–„â–ˆ â–„â–„â–„ â–ˆâ–„ â–€â–ˆ  â–€â–„ â–ˆ â–„â–ˆ â–ˆ  â–„â–„â–„ â–€â–€â–ˆâ–ˆâ–ˆ
2025-06-22 23:22:33,322 - whatsapp - WARNING - _read_output:109 - Failed to parse JSON from Node.js: â–ˆâ–€ â–„  â–ˆâ–„â–ˆ â–€â–ˆ  â–„â–€â–€â–€â–ˆâ–„â–ˆâ–ˆâ–€â–€ â–„â–ˆ â–ˆâ–„â–ˆ â–„ â–€â–€â–€â–€â–„â–ˆ â–ˆ â–ˆâ–„â–„â–€â–€  â–ˆâ–„â–ˆ â–€â–ˆâ–ˆâ–„â–ˆ
2025-06-22 23:22:33,322 - whatsapp - WARNING - _read_output:109 - Failed to parse JSON from Node.js: â–ˆ  â–ˆâ–€â–„â–„â–„â–„ â–ˆ â–ˆâ–„â–€â–ˆâ–„ â–„ â–„â–€â–€â–ˆâ–ˆâ–ˆâ–ˆâ–„â–„   â–ˆâ–ˆâ–€   â–ˆâ–€â–€â–€â–„â–ˆâ–ˆâ–ˆâ–„â–„â–ˆ  â–„ â–„â–ˆâ–„â–„â–€â–ˆ
2025-06-22 23:22:33,322 - whatsapp - WARNING - _read_output:109 - Failed to parse JSON from Node.js: â–ˆâ–ˆ â–„â–€â–„â–€â–„ â–„â–„â–€â–„â–ˆâ–ˆâ–„â–ˆâ–ˆ â–€â–€â–ˆâ–€  â–€â–ˆâ–€â–„â–„â–€â–€â–€â–ˆ â–„â–„â–ˆ â–ˆâ–€  â–€â–„â–€ â–ˆâ–ˆâ–ˆ â–€â–ˆâ–„ â–€â–„â–„â–ˆ
2025-06-22 23:22:33,322 - whatsapp - WARNING - _read_output:109 - Failed to parse JSON from Node.js: â–ˆâ–€â–€â–„â–ˆâ–€â–€â–„ â–€â–€â–„â–ˆâ–„â–„â–„  â–€â–€ â–€â–€ â–„â–„â–„  â–€ â–ˆ  â–ˆ â–€â–€â–ˆâ–„ â–ˆâ–ˆâ–„â–„â–„â–„ â–„â–€â–„  â–„â–€â–„ â–„â–ˆ
2025-06-22 23:22:33,322 - whatsapp - WARNING - _read_output:109 - Failed to parse JSON from Node.js: â–ˆâ–ˆâ–„ â–€â–ˆâ–€â–„â–€â–€â–ˆâ–„â–„â–„â–€â–€â–ˆâ–ˆâ–„ â–€â–ˆâ–€â–€ â–„â–„â–„â–ˆâ–ˆ â–„â–€â–ˆâ–€â–„â–ˆâ–„â–€â–€â–„â–ˆâ–„â–„â–„â–ˆâ–€â–„â–ˆ â–„â–€â–€â–„â–„ â–„â–ˆâ–ˆ
2025-06-22 23:22:33,322 - whatsapp - WARNING - _read_output:109 - Failed to parse JSON from Node.js: â–ˆâ–„  â–€ â–ˆâ–„ â–€â–€â–ˆâ–„â–€ â–„ â–„ â–€  â–„â–„â–„â–€â–ˆâ–„ â–„ â–€ â–„ â–ˆ â–€â–„â–ˆâ–„  â–„â–ˆâ–€â–ˆâ–ˆâ–€ â–ˆâ–€    â–ˆâ–„â–ˆ
2025-06-22 23:22:33,322 - whatsapp - WARNING - _read_output:109 - Failed to parse JSON from Node.js: â–ˆâ–„â–ˆâ–ˆâ–„â–ˆ â–„â–„â–„â–€â–ˆâ–€â–„â–ˆ   â–€â–ˆ â–ˆ â–€ â–„â–€ â–„   â–ˆâ–„â–„â–€â–€â–€â–„â–ˆâ–€â–€â–€â–ˆâ–„â–„â–ˆâ–€â–„â–ˆâ–„â–„ â–€â–ˆâ–„â–ˆ â–ˆ
2025-06-22 23:22:33,322 - whatsapp - WARNING - _read_output:109 - Failed to parse JSON from Node.js: â–ˆâ–ˆâ–„â–€â–€ â–ˆâ–„â–ˆâ–„â–€ â–„â–€â–€â–€â–„â–ˆâ–ˆâ–„  â–ˆ â–„â–ˆâ–ˆâ–ˆâ–„â–€â–„â–„â–€â–ˆâ–„â–ˆâ–„ â–„â–€â–€â–€ â–„â–€â–„â–„ â–ˆâ–„â–„â–ˆâ–ˆâ–ˆâ–ˆâ–„â–€â–ˆâ–ˆ
2025-06-22 23:22:33,323 - whatsapp - WARNING - _read_output:109 - Failed to parse JSON from Node.js: â–ˆâ–„ â–„â–€â–„â–€â–„ â–ˆâ–€â–ˆ â–„â–„â–„â–ˆâ–ˆâ–€â–€â–€â–ˆ â–ˆ â–€â–ˆâ–„ â–ˆâ–ˆâ–„â–„â–€â–ˆâ–„â–€â–ˆâ–ˆâ–ˆ â–„  â–€â–ˆâ–„â–ˆâ–ˆâ–ˆâ–„â–„â–ˆâ–ˆ â–€â–„â–„â–ˆ
2025-06-22 23:22:33,323 - whatsapp - WARNING - _read_output:109 - Failed to parse JSON from Node.js: â–ˆ â–€ â–€â–€â–„â–„â–ˆâ–ˆ â–€â–€â–ˆ â–„â–„â–€â–€â–€ â–„â–€â–„â–„â–„â–€â–ˆâ–€ â–€â–ˆâ–ˆâ–ˆâ–€â–ˆâ–„â–„ â–€â–ˆâ–„â–ˆâ–ˆâ–ˆâ–€â–„â–„â–„â–€â–„ â–ˆâ–ˆ â–ˆâ–„ â–ˆ
2025-06-22 23:22:33,323 - whatsapp - WARNING - _read_output:109 - Failed to parse JSON from Node.js: â–ˆâ–ˆâ–ˆâ–ˆâ–ˆâ–ˆâ–ˆâ–„â–„ â–€â–ˆ â–€â–€â–€â–€â–„  â–ˆ  â–€ â–ˆâ–„ â–„â–„â–„  â–€ â–ˆâ–„ â–€â–€â–ˆâ–€â–„â–„â–€â–ˆâ–ˆâ–ˆâ–€ â–„â–„â–„  â–€ â–€â–ˆ
2025-06-22 23:22:33,323 - whatsapp - WARNING - _read_output:109 - Failed to parse JSON from Node.js: â–ˆ â–„â–„â–„â–„â–„ â–ˆâ–ˆâ–„â–„ â–ˆâ–€â–ˆâ–„â–„â–„â–ˆâ–„â–ˆâ–ˆâ–„â–ˆâ–„â–ˆ â–ˆâ–„â–ˆ â–„ â–€â–€â–„â–ˆâ–ˆ â–€ â–„â–€â–€ â–€â–ˆâ–€ â–ˆâ–„â–ˆ  â–€â–ˆâ–€â–ˆ
2025-06-22 23:22:33,323 - whatsapp - WARNING - _read_output:109 - Failed to parse JSON from Node.js: â–ˆ â–ˆ   â–ˆ â–ˆâ–€â–„â–€â–ˆâ–ˆ â–ˆâ–€â–ˆâ–€â–€ â–„â–„â–ˆ â–€   â–„   â–„â–€â–€â–€â–ˆâ–„â–ˆ â–€â–„â–ˆâ–ˆâ–„â–„â–€â–„ â–„â–„â–„  â–„â–€â–„â–ˆ
2025-06-22 23:22:33,323 - whatsapp - WARNING - _read_output:109 - Failed to parse JSON from Node.js: â–ˆ â–ˆâ–„â–„â–„â–ˆ â–ˆ â–€ â–„â–€ â–€  â–„ â–„â–€ â–ˆâ–ˆâ–€â–„ â–ˆâ–ˆâ–ˆâ–€â–ˆ â–€â–€â–€â–„â–€  â–„ â–ˆâ–ˆâ–„â–ˆ â–„ â–„ â–ˆâ–€â–ˆâ–ˆâ–ˆâ–€â–ˆ
2025-06-22 23:22:33,323 - whatsapp - WARNING - _read_output:109 - Failed to parse JSON from Node.js: â–ˆâ–„â–„â–„â–„â–„â–„â–„â–ˆâ–„â–„â–„â–ˆâ–ˆâ–„â–„â–„â–ˆâ–ˆâ–ˆâ–„â–„â–„â–„â–„â–ˆâ–ˆâ–ˆâ–„â–„â–ˆâ–ˆâ–ˆâ–„â–ˆâ–ˆâ–„â–ˆâ–„â–ˆâ–„â–„â–„â–ˆâ–ˆâ–ˆâ–„â–ˆâ–„â–„â–ˆâ–„â–„â–„â–„â–ˆâ–ˆâ–ˆâ–ˆ
2025-06-22 23:22:33,323 - whatsapp - WARNING - _read_output:109 - Failed to parse JSON from Node.js: 
2025-06-22 23:22:33,323 - whatsapp - INFO - _handle_event:160 - QR code received for WhatsApp authentication
2025-06-22 23:22:33,323 - whatsapp - ERROR - _handle_event:177 - Error in event handler for qr_code: no running event loop
2025-06-22 23:22:33,328 - whatsapp - ERROR - _handle_event:177 - Error in event handler for qr_code: no running event loop
2025-06-22 23:23:00,166 - whatsapp - WARNING - _read_output:109 - Failed to parse JSON from Node.js: [INFO] Received SIGINT, shutting down gracefully
2025-06-22 23:23:00,168 - whatsapp - WARNING - _read_output:109 - Failed to parse JSON from Node.js: [INFO] Shutting down WhatsApp client...
2025-06-22 23:23:03,509 - whatsapp - INFO - stop:155 - Stopping WhatsApp integration...
2025-06-22 23:23:03,509 - whatsapp - INFO - stop_monitoring:102 - Connection monitoring stopped
2025-06-22 23:23:03,510 - whatsapp - INFO - stop:277 - Stopping WhatsApp client...
2025-06-22 23:23:03,510 - whatsapp - WARNING - _send_command:189 - Cannot send command logout: client not running
2025-06-22 23:23:03,510 - whatsapp - INFO - stop:313 - WhatsApp client stopped
2025-06-22 23:23:03,514 - whatsapp - INFO - stop:168 - WhatsApp integration stopped
2025-06-22 23:23:48,156 - whatsapp - INFO - __init__:49 - WhatsApp manager initialized
2025-06-22 23:23:48,156 - whatsapp - INFO - initialize:58 - Initializing WhatsApp components...
2025-06-22 23:23:48,156 - whatsapp - INFO - __init__:44 - WhatsApp client initialized
2025-06-22 23:23:48,156 - whatsapp - INFO - __init__:55 - Authentication handler initialized
2025-06-22 23:23:48,156 - whatsapp - INFO - __init__:67 - Connection manager initialized
2025-06-22 23:23:48,156 - whatsapp - INFO - __init__:36 - Message listener initialized
2025-06-22 23:23:48,157 - whatsapp - INFO - __init__:38 - Message sender initialized
2025-06-22 23:23:48,176 - whatsapp - INFO - initialize:81 - WhatsApp components initialized successfully
2025-06-22 23:23:50,473 - whatsapp - INFO - start:123 - Starting WhatsApp integration...
2025-06-22 23:23:50,474 - whatsapp - INFO - start_monitoring:88 - Connection monitoring started
2025-06-22 23:23:50,474 - whatsapp - INFO - start:65 - Starting WhatsApp Web.js client...
2025-06-22 23:23:50,478 - whatsapp - INFO - start:92 - WhatsApp client process started
2025-06-22 23:23:50,482 - whatsapp - INFO - start:136 - WhatsApp integration started successfully
2025-06-22 23:23:50,483 - whatsapp - INFO - _attempt_reconnection:177 - Attempting reconnection (attempt 1/5)
2025-06-22 23:23:50,483 - whatsapp - INFO - stop:302 - Stopping WhatsApp client...
2025-06-22 23:23:50,483 - whatsapp - WARNING - _send_command:214 - Cannot send command logout: client not running
2025-06-22 23:23:50,799 - whatsapp - WARNING - _read_output:109 - Failed to parse JSON from Node.js: [INFO] Starting WhatsApp client...
2025-06-22 23:23:55,485 - whatsapp - WARNING - stop:315 - Process didn't terminate gracefully, forcing...
2025-06-22 23:23:55,491 - whatsapp - INFO - stop:338 - WhatsApp client stopped
2025-06-22 23:23:57,497 - whatsapp - INFO - start:65 - Starting WhatsApp Web.js client...
2025-06-22 23:23:57,501 - whatsapp - INFO - start:92 - WhatsApp client process started
2025-06-22 23:23:57,501 - whatsapp - INFO - _attempt_reconnection:191 - Reconnection attempt initiated
2025-06-22 23:23:57,819 - whatsapp - WARNING - _read_output:109 - Failed to parse JSON from Node.js: [INFO] Starting WhatsApp client...
2025-06-22 23:24:01,391 - whatsapp - WARNING - _read_output:109 - Failed to parse JSON from Node.js: [INFO] QR Code received, scan with your phone
2025-06-22 23:24:01,410 - whatsapp - WARNING - _read_output:109 - Failed to parse JSON from Node.js: â–„â–„â–„â–„â–„â–„â–„â–„â–„â–„â–„â–„â–„â–„â–„â–„â–„â–„â–„â–„â–„â–„â–„â–„â–„â–„â–„â–„â–„â–„â–„â–„â–„â–„â–„â–„â–„â–„â–„â–„â–„â–„â–„â–„â–„â–„â–„â–„â–„â–„â–„â–„â–„â–„â–„â–„â–„â–„â–„
2025-06-22 23:24:01,410 - whatsapp - WARNING - _read_output:109 - Failed to parse JSON from Node.js: â–ˆ â–„â–„â–„â–„â–„ â–ˆâ–„â–€â–ˆ â–€â–„â–ˆâ–€â–€â–€â–„ â–€â–„â–ˆ  â–„â–ˆ â–ˆ â–„  â–€â–ˆâ–€  â–ˆâ–ˆâ–„ â–„â–€â–€ â–„ â–ˆâ–ˆ â–„â–„â–„â–„â–„ â–ˆ
2025-06-22 23:24:01,410 - whatsapp - WARNING - _read_output:109 - Failed to parse JSON from Node.js: â–ˆ â–ˆ   â–ˆ â–ˆ â–€â–„â–€â–€ â–„â–ˆâ–ˆ  â–ˆâ–„â–ˆâ–„   â–ˆâ–„â–ˆâ–€  â–ˆâ–€ â–€â–€â–€â–„â–ˆâ–ˆ â–€ â–ˆâ–€â–„ â–ˆâ–ˆ â–ˆ   â–ˆ â–ˆ
2025-06-22 23:24:01,410 - whatsapp - WARNING - _read_output:109 - Failed to parse JSON from Node.js: â–ˆ â–ˆâ–„â–„â–„â–ˆ â–ˆâ–„â–€â–€â–„â–ˆâ–€â–€â–€â–€â–€â–ˆ â–€â–€â–€ â–ˆâ–€ â–„â–„â–„ â–„â–„â–€â–ˆâ–ˆâ–€â–„â–„ â–„â–ˆâ–ˆ â–„â–„â–€â–„â–ˆâ–ˆ â–ˆâ–„â–„â–„â–ˆ â–ˆ
2025-06-22 23:24:01,410 - whatsapp - WARNING - _read_output:109 - Failed to parse JSON from Node.js: â–ˆâ–„â–„â–„â–„â–„â–„â–„â–ˆâ–„â–ˆ â–ˆâ–„â–ˆâ–„â–ˆâ–„â–€ â–€â–„â–€ â–€â–„â–ˆ â–ˆâ–„â–ˆ â–€ â–€ â–ˆ â–ˆ â–€ â–€ â–€â–„â–ˆ â–ˆ â–ˆâ–„â–„â–„â–„â–„â–„â–„â–ˆ
2025-06-22 23:24:01,410 - whatsapp - WARNING - _read_output:109 - Failed to parse JSON from Node.js: â–ˆ    â–€â–€â–„â–€â–„ â–„ â–„â–€â–ˆâ–ˆâ–„ â–€â–ˆâ–„    â–€â–„â–„â–„   â–ˆ â–„â–€â–ˆâ–ˆâ–ˆ â–„â–„â–€ â–ˆâ–ˆâ–ˆâ–€â–€ â–ˆâ–€ â–„ â–ˆâ–„â–ˆ
2025-06-22 23:24:01,411 - whatsapp - WARNING - _read_output:109 - Failed to parse JSON from Node.js: â–ˆâ–„â–„â–€  â–„â–„â–€ â–ˆâ–„ â–ˆ â–€ â–€ â–ˆâ–ˆ â–„â–„â–„ â–ˆâ–„â–„â–€â–€ â–ˆâ–„â–ˆâ–„ â–„ â–€â–„ â–€ â–„â–„â–„ â–„â–„ â–„ â–€ â–„â–„â–„â–ˆ
2025-06-22 23:24:01,411 - whatsapp - WARNING - _read_output:109 - Failed to parse JSON from Node.js: â–ˆâ–€ â–„ â–€â–€â–„â–„â–ˆâ–„â–ˆâ–€â–ˆâ–„  â–ˆâ–ˆâ–„â–„ â–ˆâ–„â–„â–„â–ˆâ–€â–€â–ˆâ–ˆâ–€ â–ˆâ–€â–ˆâ–„ â–ˆâ–€â–€â–ˆâ–„â–ˆ â–ˆ â–ˆâ–„â–„â–€â–€â–ˆâ–ˆâ–ˆâ–„ â–„â–ˆ
2025-06-22 23:24:01,411 - whatsapp - WARNING - _read_output:109 - Failed to parse JSON from Node.js: â–ˆâ–ˆâ–ˆâ–€  â–ˆâ–„ â–ˆ â–ˆâ–„â–ˆ â–€â–ˆ â–ˆ â–€â–„â–„â–ˆ â–„ â–„â–€â–„â–ˆâ–ˆ   â–ˆâ–„â–€â–ˆ â–ˆ â–€ â–€â–€â–€ â–„â–ˆâ–„â–ˆâ–ˆâ–€  â–„â–ˆâ–ˆ
2025-06-22 23:24:01,411 - whatsapp - WARNING - _read_output:109 - Failed to parse JSON from Node.js: â–ˆ â–ˆâ–€â–„â–„â–„â–„â–ˆ  â–ˆâ–ˆâ–„  â–€ â–„â–€â–ˆâ–„ â–€ â–ˆâ–€â–€â–„â–„â–€â–ˆ  â–„â–€â–€â–ˆâ–„â–„â–€â–ˆ â–„â–€  â–€â–€â–ˆâ–„â–„â–„â–€ â–„â–ˆâ–€â–ˆ
2025-06-22 23:24:01,411 - whatsapp - WARNING - _read_output:109 - Failed to parse JSON from Node.js: â–ˆâ–ˆâ–€â–€â–€â–„ â–„â–„â–ˆâ–„   â–€â–€ â–ˆâ–ˆâ–„ â–€â–„â–„  â–€ â–€ â–„â–„â–€â–ˆâ–ˆâ–„â–„ â–„â–€â–ˆâ–ˆâ–ˆâ–„â–€â–„â–€â–€â–„â–ˆâ–€â–€â–€â–€â–ˆâ–ˆâ–€â–ˆâ–ˆ
2025-06-22 23:24:01,411 - whatsapp - WARNING - _read_output:109 - Failed to parse JSON from Node.js: â–ˆâ–€â–€   â–„â–„â–„ â–€  â–„ â–ˆâ–ˆ  â–ˆâ–ˆâ–ˆâ–„â–„â–€â–ˆâ–€â–€ â–„  â–€â–€â–„â–ˆâ–ˆâ–€â–€â–ˆ â–„â–„  â–€â–ˆâ–„â–„ â–€â–„   â–€â–€ â–ˆ
2025-06-22 23:24:01,411 - whatsapp - WARNING - _read_output:109 - Failed to parse JSON from Node.js: â–ˆâ–„ â–„â–ˆâ–ˆ â–„â–ˆâ–ˆâ–„â–ˆâ–ˆâ–€ â–€â–€â–„â–„  â–„â–€â–€  â–ˆâ–€â–€â–ˆâ–„â–ˆ â–„â–€â–„â–ˆâ–€â–€â–„â–„  â–„ â–„â–„â–€â–ˆ   â–ˆ â–€â–ˆâ–ˆ â–ˆ
2025-06-22 23:24:01,411 - whatsapp - WARNING - _read_output:109 - Failed to parse JSON from Node.js: â–ˆ â–ˆâ–€ â–„â–€â–„â–€â–€â–€ â–€ â–€â–€ â–„ â–„â–ˆ â–€â–ˆâ–ˆâ–„â–ˆâ–ˆ â–€â–ˆâ–„â–„â–„â–„â–ˆ   â–€â–€â–€â–ˆâ–„ â–ˆâ–€â–„â–„â–„â–€â–€â–ˆâ–ˆ â–ˆâ–„ â–ˆ
2025-06-22 23:24:01,411 - whatsapp - WARNING - _read_output:109 - Failed to parse JSON from Node.js: â–ˆâ–„â–€â–ˆâ–€ â–„â–„â–„ â–ˆâ–€â–ˆâ–€â–„â–ˆ  â–„â–„â–€â–€â–ˆâ–„â–€â–€â–„ â–„â–„â–„ â–ˆ â–„â–ˆâ–€â–ˆâ–„â–ˆ â–€â–„ â–„â–€â–„â–„â–€ â–„â–„â–„ â–€â–€â–ˆâ–ˆâ–ˆ
2025-06-22 23:24:01,411 - whatsapp - WARNING - _read_output:109 - Failed to parse JSON from Node.js: â–ˆâ–„â–ˆâ–„â–„ â–ˆâ–„â–ˆ  â–„â–€â–ˆâ–ˆâ–€â–€ â–ˆâ–„â–ˆâ–„â–€â–€ â–ˆâ–ˆ â–ˆâ–„â–ˆ â–ˆâ–„â–€ â–ˆâ–ˆâ–ˆâ–ˆ â–ˆâ–€â–„â–„â–„â–„ â–„ â–ˆâ–„â–ˆ â–€â–€â–ˆâ–€â–ˆ
2025-06-22 23:24:01,411 - whatsapp - WARNING - _read_output:109 - Failed to parse JSON from Node.js: â–ˆâ–€â–„â–„â–ˆ  â–„  â–ˆ  â–„â–€â–€â–ˆâ–€â–ˆâ–€ â–„â–ˆâ–€ â–€â–„     â–€â–„â–€ â–ˆâ–ˆâ–ˆâ–„â–€â–„  â–€â–„â–ˆâ–„â–„ â–„â–„â–„ â–ˆâ–„â–€â–€â–ˆ
2025-06-22 23:24:01,411 - whatsapp - WARNING - _read_output:109 - Failed to parse JSON from Node.js: â–ˆ   â–ˆ â–€â–„ â–ˆâ–„â–€ â–ˆ   â–ˆ â–€â–€â–„â–„â–„ â–€â–ˆ  â–ˆâ–€â–€ â–ˆ â–„ â–ˆ â–„â–„â–„â–€ â–€â–€â–ˆâ–„â–ˆâ–„â–ˆâ–„â–„â–ˆâ–ˆâ–ˆâ–ˆ â–ˆ
2025-06-22 23:24:01,411 - whatsapp - WARNING - _read_output:109 - Failed to parse JSON from Node.js: â–ˆ â–„â–€â–„â–€â–ˆâ–„â–ˆâ–„â–„â–ˆâ–„â–ˆ â–€â–€ â–€â–ˆâ–€â–„â–„â–ˆâ–„â–ˆ â–€â–„â–ˆ â–ˆâ–ˆâ–€â–€â–„ â–€â–„â–€â–„â–ˆâ–„â–€â–€â–„â–ˆâ–€â–„â–€ â–„â–ˆ â–€â–ˆ â–„â–ˆ
2025-06-22 23:24:01,411 - whatsapp - WARNING - _read_output:109 - Failed to parse JSON from Node.js: â–ˆâ–€â–ˆâ–ˆ  â–€â–„â–ˆ  â–ˆâ–€â–„â–€â–„â–ˆâ–ˆ â–„â–€â–ˆ â–€ â–„â–ˆâ–€â–ˆâ–ˆâ–€â–„â–€â–€ â–„ â–„â–€ â–ˆâ–€â–„â–ˆ â–€ â–„â–ˆâ–€ â–€ â–ˆâ–„â–„â–„â–ˆâ–ˆ
2025-06-22 23:24:01,411 - whatsapp - WARNING - _read_output:109 - Failed to parse JSON from Node.js: â–ˆâ–„â–ˆâ–„â–„ â–€â–„â–ˆ â–ˆâ–„â–€ â–ˆâ–„ â–„ â–ˆ  â–ˆ â–„â–€â–„  â–„â–€â–ˆ â–„â–€â–„â–ˆâ–„ â–„â–€     â–€â–€ â–ˆâ–ˆâ–ˆâ–„â–ˆ  â–ˆâ–„â–ˆ
2025-06-22 23:24:01,411 - whatsapp - WARNING - _read_output:109 - Failed to parse JSON from Node.js: â–ˆ â–ˆâ–€â–ˆ â–„â–„â–€â–ˆâ–ˆâ–ˆâ–€  â–€â–€â–€ â–€ â–ˆ â–ˆâ–ˆâ–„â–€ â–„   â–ˆâ–„â–„â–€â–€â–€â–€â–„â–€â–€ â–„â–ˆ â–€ â–„â–ˆâ–€â–„ â–€â–€â–ˆâ–ˆ â–ˆ
2025-06-22 23:24:01,411 - whatsapp - WARNING - _read_output:109 - Failed to parse JSON from Node.js: â–ˆâ–€â–€â–ˆâ–„â–ˆâ–ˆâ–„â–ˆâ–€ â–€â–€â–€â–„  â–ˆâ–ˆâ–„ â–ˆâ–ˆ â–€ â–ˆâ–ˆâ–€   â–€â–ˆ â–ˆâ–ˆâ–€ â–„â–„  â–„â–ˆâ–„â–€â–„â–„ â–„â–€ â–„â–ˆâ–„â–„â–€â–ˆ
2025-06-22 23:24:01,411 - whatsapp - WARNING - _read_output:109 - Failed to parse JSON from Node.js: â–ˆ â–€â–€â–„â–€â–€â–„ â–€â–„â–€ â–€â–€â–„â–ˆâ–ˆâ–€â–€ â–„ â–ˆâ–€â–€â–ˆâ–„â–ˆâ–„â–„â–€â–€â–€ â–ˆâ–€â–€ â–„â–€   â–ˆâ–ˆ â–„â–ˆ â–„â–„â–„â–„â–€â–„â–ˆ â–ˆ
2025-06-22 23:24:01,411 - whatsapp - WARNING - _read_output:109 - Failed to parse JSON from Node.js: â–ˆ â–€ â–€â–€â–„â–„â–€ â–ˆâ–€â–„â–„â–€â–ˆâ–ˆâ–€ â–ˆâ–ˆâ–„â–„â–€â–€â–„â–€â–€â–„â–ˆâ–ˆâ–ˆâ–€â–ˆâ–€â–„  â–ˆâ–„â–€â–„  â–ˆâ–„  â–€â–€â–ˆ â–ˆâ–„ â–„â–„â–„â–ˆ
2025-06-22 23:24:01,411 - whatsapp - WARNING - _read_output:109 - Failed to parse JSON from Node.js: â–ˆâ–ˆâ–ˆâ–ˆâ–ˆâ–ˆâ–ˆâ–„â–ˆ â–€  â–ˆ â–ˆ â–ˆâ–„â–„â–„ â–ˆâ–€ â–ˆâ–ˆ â–„â–„â–„  â–€ â–ˆâ–€â–„ â–€â–ˆâ–€â–„â–ˆâ–„â–€ â–„â–€ â–„â–„â–„ â–„â–„â–€ â–ˆ
2025-06-22 23:24:01,412 - whatsapp - WARNING - _read_output:109 - Failed to parse JSON from Node.js: â–ˆ â–„â–„â–„â–„â–„ â–ˆâ–ˆ â–ˆ   â–„â–ˆâ–„â–€â–„â–€â–ˆâ–€â–ˆ â–€â–ˆ â–ˆâ–„â–ˆ â–ˆâ–€â–ˆâ–ˆâ–ˆâ–„â–€â–ˆâ–„â–„â–€â–€â–„â–„â–ˆâ–„â–ˆ â–ˆâ–„â–ˆ   â–ˆâ–€â–ˆ
2025-06-22 23:24:01,412 - whatsapp - WARNING - _read_output:109 - Failed to parse JSON from Node.js: â–ˆ â–ˆ   â–ˆ â–ˆâ–€â–ˆâ–„â–€â–„â–„â–€â–€â–ˆâ–€â–ˆ â–„ â–ˆ â–€â–ˆ     â–„ â–ˆ â–ˆâ–ˆâ–ˆâ–ˆ â–ˆ â–„    â–ˆ â–„  â–„â–€â–ˆ â–„â–ˆ
2025-06-22 23:24:01,412 - whatsapp - WARNING - _read_output:109 - Failed to parse JSON from Node.js: â–ˆ â–ˆâ–„â–„â–„â–ˆ â–ˆ â–ˆ â–€ â–„â–ˆ   â–„â–„â–€â–€â–€â–ˆâ–€â–„â–ˆâ–ˆâ–ˆâ–ˆâ–€â–„    â–€â–ˆâ–ˆâ–€  â–€â–ˆâ–ˆâ–ˆ â–€â–„â–€â–€ â–€â–„â–„â–ˆâ–€â–ˆ
2025-06-22 23:24:01,412 - whatsapp - WARNING - _read_output:109 - Failed to parse JSON from Node.js: â–ˆâ–„â–„â–„â–„â–„â–„â–„â–ˆâ–„â–„â–ˆâ–ˆâ–ˆâ–ˆâ–ˆâ–„â–ˆâ–ˆâ–ˆâ–„â–ˆâ–„â–„â–ˆâ–„â–ˆâ–ˆâ–„â–„â–ˆâ–ˆâ–ˆâ–„â–ˆâ–ˆâ–ˆâ–ˆâ–„â–ˆâ–„â–„â–ˆâ–ˆâ–ˆâ–ˆâ–ˆâ–„â–ˆâ–„â–ˆâ–ˆâ–„â–„â–„â–ˆâ–ˆâ–ˆâ–ˆ
2025-06-22 23:24:01,412 - whatsapp - WARNING - _read_output:109 - Failed to parse JSON from Node.js: 
2025-06-22 23:24:01,412 - whatsapp - INFO - _handle_event:160 - QR code received for WhatsApp authentication
2025-06-22 23:24:01,413 - whatsapp - INFO - _handle_qr_code:83 - QR code received for WhatsApp authentication
2025-06-22 23:24:01,443 - whatsapp - INFO - _handle_qr_code:244 - QR code received for WhatsApp authentication
2025-06-22 23:24:39,863 - whatsapp - WARNING - _read_output:109 - Failed to parse JSON from Node.js: [INFO] Received SIGINT, shutting down gracefully
2025-06-22 23:24:39,863 - whatsapp - WARNING - _read_output:109 - Failed to parse JSON from Node.js: [INFO] Shutting down WhatsApp client...
2025-06-22 23:24:40,440 - whatsapp - INFO - stop:155 - Stopping WhatsApp integration...
2025-06-22 23:24:40,440 - whatsapp - INFO - stop_monitoring:102 - Connection monitoring stopped
2025-06-22 23:24:40,440 - whatsapp - INFO - stop:302 - Stopping WhatsApp client...
2025-06-22 23:24:40,440 - whatsapp - WARNING - _send_command:214 - Cannot send command logout: client not running
2025-06-22 23:24:40,915 - whatsapp - INFO - stop:338 - WhatsApp client stopped
2025-06-22 23:24:40,920 - whatsapp - INFO - stop:168 - WhatsApp integration stopped
2025-06-22 23:24:52,246 - whatsapp - INFO - __init__:49 - WhatsApp manager initialized
2025-06-22 23:24:52,246 - whatsapp - INFO - initialize:58 - Initializing WhatsApp components...
2025-06-22 23:24:52,246 - whatsapp - INFO - __init__:44 - WhatsApp client initialized
2025-06-22 23:24:52,247 - whatsapp - INFO - __init__:55 - Authentication handler initialized
2025-06-22 23:24:52,247 - whatsapp - INFO - __init__:67 - Connection manager initialized
2025-06-22 23:24:52,247 - whatsapp - INFO - __init__:36 - Message listener initialized
2025-06-22 23:24:52,247 - whatsapp - INFO - __init__:38 - Message sender initialized
2025-06-22 23:24:52,268 - whatsapp - INFO - initialize:81 - WhatsApp components initialized successfully
2025-06-22 23:24:54,618 - whatsapp - INFO - start:123 - Starting WhatsApp integration...
2025-06-22 23:24:54,618 - whatsapp - INFO - start_monitoring:88 - Connection monitoring started
2025-06-22 23:24:54,618 - whatsapp - INFO - start:65 - Starting WhatsApp Web.js client...
2025-06-22 23:24:54,622 - whatsapp - INFO - start:92 - WhatsApp client process started
2025-06-22 23:24:54,627 - whatsapp - INFO - start:136 - WhatsApp integration started successfully
2025-06-22 23:24:54,628 - whatsapp - INFO - _attempt_reconnection:177 - Attempting reconnection (attempt 1/5)
2025-06-22 23:24:54,628 - whatsapp - INFO - stop:302 - Stopping WhatsApp client...
2025-06-22 23:24:54,628 - whatsapp - WARNING - _send_command:214 - Cannot send command logout: client not running
2025-06-22 23:24:54,936 - whatsapp - WARNING - _read_output:109 - Failed to parse JSON from Node.js: [INFO] Starting WhatsApp client...
2025-06-22 23:24:59,637 - whatsapp - WARNING - stop:315 - Process didn't terminate gracefully, forcing...
2025-06-22 23:24:59,643 - whatsapp - INFO - stop:338 - WhatsApp client stopped
2025-06-22 23:25:01,669 - whatsapp - INFO - start:65 - Starting WhatsApp Web.js client...
2025-06-22 23:25:01,673 - whatsapp - INFO - start:92 - WhatsApp client process started
2025-06-22 23:25:01,673 - whatsapp - INFO - _attempt_reconnection:191 - Reconnection attempt initiated
2025-06-22 23:25:01,985 - whatsapp - WARNING - _read_output:109 - Failed to parse JSON from Node.js: [INFO] Starting WhatsApp client...
2025-06-22 23:25:05,531 - whatsapp - WARNING - _read_output:109 - Failed to parse JSON from Node.js: [INFO] QR Code received, scan with your phone
2025-06-22 23:25:05,550 - whatsapp - WARNING - _read_output:109 - Failed to parse JSON from Node.js: â–„â–„â–„â–„â–„â–„â–„â–„â–„â–„â–„â–„â–„â–„â–„â–„â–„â–„â–„â–„â–„â–„â–„â–„â–„â–„â–„â–„â–„â–„â–„â–„â–„â–„â–„â–„â–„â–„â–„â–„â–„â–„â–„â–„â–„â–„â–„â–„â–„â–„â–„â–„â–„â–„â–„â–„â–„â–„â–„
2025-06-22 23:25:05,550 - whatsapp - WARNING - _read_output:109 - Failed to parse JSON from Node.js: â–ˆ â–„â–„â–„â–„â–„ â–ˆâ–„â–€â–ˆâ–€  â–€â–€â–€â–€â–ˆ â–ˆâ–ˆâ–€â–„â–„â–„â–€â–„â–€ â–„â–€â–€â–€â–ˆâ–€â–ˆ â–€â–„â–„   â–€ â–„ â–ˆâ–ˆ â–„â–„â–„â–„â–„ â–ˆ
2025-06-22 23:25:05,550 - whatsapp - WARNING - _read_output:109 - Failed to parse JSON from Node.js: â–ˆ â–ˆ   â–ˆ â–ˆ   â–„â–€â–„â–ˆâ–ˆâ–ˆ  â–ˆâ–„â–ˆâ–„â–€  â–ˆ â–ˆâ–ˆâ–„ â–ˆâ–€   â–€â–ˆâ–„â–ˆâ–„â–€ â–ˆâ–€â–„ â–ˆâ–ˆ â–ˆ   â–ˆ â–ˆ
2025-06-22 23:25:05,550 - whatsapp - WARNING - _read_output:109 - Failed to parse JSON from Node.js: â–ˆ â–ˆâ–„â–„â–„â–ˆ â–ˆâ–„  â–ˆâ–„â–„â–ˆâ–€â–€â–„  â–€â–€â–€ â–ˆâ–€ â–„â–„â–„  â–ˆâ–€â–ˆâ–„â–ˆâ–„ â–€â–„â–ˆâ–€â–„â–ˆâ–„â–€â–„â–ˆâ–ˆ â–ˆâ–„â–„â–„â–ˆ â–ˆ
2025-06-22 23:25:05,550 - whatsapp - WARNING - _read_output:109 - Failed to parse JSON from Node.js: â–ˆâ–„â–„â–„â–„â–„â–„â–„â–ˆâ–„â–€â–„â–ˆ â–ˆâ–„â–ˆâ–„â–ˆâ–„â–€â–„â–€â–„â–€â–„â–ˆ â–ˆâ–„â–ˆ â–€ â–€ â–ˆ â–€ â–ˆâ–„â–€ â–ˆ â–€ â–ˆ â–ˆâ–„â–„â–„â–„â–„â–„â–„â–ˆ
2025-06-22 23:25:05,550 - whatsapp - WARNING - _read_output:109 - Failed to parse JSON from Node.js: â–ˆ  â–„ â–€â–€â–„â–€ â–€â–€â–ˆâ–€â–ˆâ–€â–„â–ˆâ–„â–€â–ˆâ–„  â–„ â–€â–„â–„    â–ˆ â–„â–€â–€ â–„â–„   â–€â–€â–„â–ˆ â–€ â–ˆâ–€â–„â–„ â–€â–„â–ˆ
2025-06-22 23:25:05,550 - whatsapp - WARNING - _read_output:109 - Failed to parse JSON from Node.js: â–ˆâ–ˆâ–€ â–ˆâ–„â–„â–„â–€â–ˆâ–€â–€ â–ˆ â–€â–„â–ˆ â–ˆâ–ˆ â–„â–„â–„â–„â–ˆâ–ˆâ–„â–ˆ â–€â–ˆ â–ˆ â–ˆâ–ˆâ–ˆâ–ˆâ–€â–„ â–€â–ˆâ–„â–„â–„ â–ˆâ–„ â–€â–€ â–„â–„â–„â–ˆ
2025-06-22 23:25:05,550 - whatsapp - WARNING - _read_output:109 - Failed to parse JSON from Node.js: â–ˆâ–„â–€â–€â–„â–ˆâ–ˆâ–„â–„ â–„â–ˆâ–„â–„â–„ â–€â–ˆâ–ˆâ–„  â–ˆâ–„â–ˆâ–ˆâ–€â–€â–€â–€â–ˆâ–€ â–€ â–„  â–€ â–„â–ˆâ–ˆâ–ˆâ–„â–€â–ˆâ–ˆâ–ˆâ–ˆâ–€ â–€â–ˆâ–€ â–ˆâ–„â–ˆ
2025-06-22 23:25:05,550 - whatsapp - WARNING - _read_output:109 - Failed to parse JSON from Node.js: â–ˆâ–„ â–ˆâ–€â–ˆâ–ˆâ–„â–€ â–ˆâ–€ â–„â–€â–„  â–„â–ˆâ–ˆâ–ˆ  â–„â–€â–€â–ˆâ–€â–ˆâ–€â–€ â–€â–„â–ˆâ–„â–ˆâ–€â–„â–„â–€â–„â–„â–ˆ â–„â–ˆâ–„â–€â–€â–ˆâ–„ â–€â–€â–ˆâ–€â–ˆ
2025-06-22 23:25:05,550 - whatsapp - WARNING - _read_output:109 - Failed to parse JSON from Node.js: â–ˆâ–ˆâ–ˆâ–€â–ˆâ–€â–ˆâ–„  â–€ â–ˆâ–„  â–€â–€â–ˆâ–€â–ˆâ–ˆâ–„â–ˆâ–€â–ˆâ–ˆâ–„â–„â–„â–ˆâ–ˆâ–ˆâ–„â–„â–€â–€â–ˆâ–ˆâ–ˆ â–ˆâ–€â–„  â–ˆ  â–ˆ â–ˆâ–„â–ˆâ–ˆâ–€ â–„â–ˆ
2025-06-22 23:25:05,550 - whatsapp - WARNING - _read_output:109 - Failed to parse JSON from Node.js: â–ˆ â–„â–„ â–€â–ˆâ–„â–€â–„â–„ â–ˆâ–„â–€â–€â–„â–ˆâ–€ â–„â–€â–„â–€ â–ˆâ–€ â–€â–„â–ˆâ–€â–€ â–ˆâ–„â–„ â–ˆâ–ˆâ–„ â–„â–ˆâ–€â–ˆ   â–ˆâ–„â–„â–ˆâ–€â–ˆâ–„â–„â–ˆâ–ˆ
2025-06-22 23:25:05,550 - whatsapp - WARNING - _read_output:109 - Failed to parse JSON from Node.js: â–ˆâ–ˆâ–€â–ˆâ–ˆ  â–„â–„â–€â–„â–„â–„â–€ â–ˆâ–ˆ â–„â–ˆâ–ˆâ–ˆâ–ˆ â–€â–ˆâ–€â–€ â–„ â–€â–€â–ˆâ–ˆâ–„ â–ˆ â–ˆâ–„ â–„â–€â–€â–€â–ˆâ–„â–„ â–„â–„â–„ â–€ â–€â–€â–ˆ
2025-06-22 23:25:05,550 - whatsapp - WARNING - _read_output:109 - Failed to parse JSON from Node.js: â–ˆâ–„â–€â–„â–ˆâ–„â–ˆâ–„â–ˆâ–ˆâ–€â–„ â–„â–„â–ˆâ–€â–„â–ˆâ–ˆ â–„â–ˆâ–ˆ  â–ˆâ–€â–€ â–„ â–€â–ˆâ–€â–„ â–ˆâ–ˆâ–€â–ˆâ–ˆâ–ˆ â–ˆ â–„â–„â–„â–ˆâ–„â–ˆâ–€â–ˆ â–„â–ˆ â–ˆ
2025-06-22 23:25:05,550 - whatsapp - WARNING - _read_output:109 - Failed to parse JSON from Node.js: â–ˆâ–€â–ˆâ–€ â–ˆ â–„â–„â–ˆâ–„â–€â–„â–„â–€â–ˆâ–€â–„ â–„â–ˆ â–€â–ˆâ–„â–„â–€â–ˆâ–ˆâ–€â–„â–ˆâ–„â–„â–„â–ˆ â–„â–€ â–€â–ˆ â–„â–€â–ˆâ–ˆâ–ˆâ–ˆâ–ˆâ–€â–€ â–ˆâ–€â–„ â–„â–ˆ
2025-06-22 23:25:05,551 - whatsapp - WARNING - _read_output:109 - Failed to parse JSON from Node.js: â–ˆ â–€â–€  â–„â–„â–„ â–€â–€â–€ â–„ â–ˆ â–„â–„â–€â–€â–ˆâ–„â–€ â–„ â–„â–„â–„ â–ˆâ–„â–„â–ˆâ–€â–„ â–„â–€â–„â–„ â–ˆâ–„ â–€â–ˆ â–„â–„â–„ â–ˆâ–€ â–€â–ˆ
2025-06-22 23:25:05,551 - whatsapp - WARNING - _read_output:109 - Failed to parse JSON from Node.js: â–ˆâ–€â–„   â–ˆâ–„â–ˆ â–„â–ˆâ–ˆâ–„â–€â–„â–€ â–ˆâ–„ â–ˆâ–€â–€ â–„â–ˆ â–ˆâ–„â–ˆ  â–„â–€â–€â–€â–€â–ˆâ–ˆâ–„â–€ â–„  â–€â–€â–ˆ â–ˆâ–„â–ˆ â–€â–„â–„ â–ˆ
2025-06-22 23:25:05,551 - whatsapp - WARNING - _read_output:109 - Failed to parse JSON from Node.js: â–ˆâ–ˆâ–ˆâ–ˆâ–€â–„ â–„â–„â–„â–ˆ  â–€ â–€â–„â–ˆâ–ˆâ–ˆâ–ˆ  â–€ â–ˆâ–ˆ  â–„  â–ˆâ–ˆ  â–ˆ â–ˆâ–„â–„ â–€â–ˆâ–€â–ˆâ–„   â–„â–„â–„ â–„â–ˆ â–€â–ˆ
2025-06-22 23:25:05,551 - whatsapp - WARNING - _read_output:109 - Failed to parse JSON from Node.js: â–ˆâ–„ â–ˆâ–„  â–„â–€â–„â–„â–„ â–„â–€ â–„â–„ â–€ â–ˆ â–„â–€â–€â–„â–€â–ˆâ–„â–€â–€â–ˆâ–€ â–„â–„â–€ â–ˆâ–€â–„â–€â–€â–ˆâ–€â–„â–ˆâ–ˆâ–ˆâ–„ â–„â–ˆâ–„â–ˆâ–„ â–ˆ
2025-06-22 23:25:05,551 - whatsapp - WARNING - _read_output:109 - Failed to parse JSON from Node.js: â–ˆ â–ˆ â–€â–„â–€â–„â–€ â–„â–„ â–ˆâ–„ â–ˆ â–ˆ  â–„â–ˆâ–€ â–„â–€â–ˆâ–ˆâ–ˆ â–ˆâ–ˆ â–€â–„â–„â–€â–ˆâ–ˆâ–„â–€â–ˆâ–ˆâ–ˆ â–„â–ˆâ–„ â–„â–„ â–ˆâ–€â–ˆâ–„â–„â–ˆ
2025-06-22 23:25:05,551 - whatsapp - WARNING - _read_output:109 - Failed to parse JSON from Node.js: â–ˆ  â–€ â–„â–€â–„â–„ â–€â–€â–„â–„â–ˆ â–ˆâ–ˆâ–„ â–€â–ˆâ–€â–€ â–„â–„â–ˆâ–ˆâ–ˆâ–„â–€â–€â–ˆ â–ˆ â–„  â–ˆâ–€â–€â–ˆâ–€â–ˆ â–„â–€ â–ˆ  â–„â–„â–€â–ˆâ–„â–ˆ
2025-06-22 23:25:05,551 - whatsapp - WARNING - _read_output:109 - Failed to parse JSON from Node.js: â–ˆ â–ˆâ–„  â–„â–„ â–€â–€â–€     â–„â–„   â–„ â–„â–€â–„â–„ â–„â–„â–ˆ â–ˆâ–€â–„ â–€â–ˆâ–ˆâ–ˆ â–„ â–„â–€ â–„â–€ â–ˆâ–ˆâ–„â–„â–€â–€â–ˆâ–„â–ˆ
2025-06-22 23:25:05,551 - whatsapp - WARNING - _read_output:109 - Failed to parse JSON from Node.js: â–ˆâ–„ â–€â–„â–€â–€â–„ â–€â–„â–„â–„â–ˆ â–€â–€ â–ˆâ–€ â–ˆâ–€â–ˆâ–€â–„â–€   â–ˆâ–€â–ˆâ–„â–„â–€â–ˆâ–ˆâ–„â–„â–ˆâ–€â–€â–ˆâ–€      â–„  â–€â–ˆâ–ˆâ–„â–ˆ
2025-06-22 23:25:05,551 - whatsapp - WARNING - _read_output:109 - Failed to parse JSON from Node.js: â–ˆ   â–„ â–„â–„â–ˆâ–€  â–„â–ˆâ–ˆâ–ˆâ–„ â–ˆâ–„  â–ˆ â–„â–ˆâ–€â–ˆâ–€â–ˆâ–ˆ â–€â–ˆ â–ˆ â–„â–€â–€â–„â–€â–ˆâ–€â–ˆâ–ˆ â–ˆâ–€ â–€â–ˆ â–€â–ˆâ–„â–„â–ˆâ–ˆ
2025-06-22 23:25:05,551 - whatsapp - WARNING - _read_output:109 - Failed to parse JSON from Node.js: â–ˆâ–€ â–€ â–ˆâ–€â–„  â–€â–„â–ˆâ–€ â–ˆ â–ˆâ–€â–€ â–ˆ â–ˆâ–€ â–„â–€â–„â–ˆâ–€â–„â–€â–€â–„â–„â–„â–ˆâ–€â–„ â–„â–€ â–€â–ˆâ–„â–„â–€â–„â–„ â–ˆâ–„â–„â–„ â–€â–ˆ
2025-06-22 23:25:05,551 - whatsapp - WARNING - _read_output:109 - Failed to parse JSON from Node.js: â–ˆ â–€ â–€â–€â–„â–„â–€â–€â–€â–ˆâ–„â–„â–„â–ˆâ–€ â–„â–ˆ   â–„â–„â–€â–€â–ˆâ–€ â–€â–€â–ˆâ–€â–€â–„ â–€â–„ â–€â–€â–ˆâ–€â–ˆâ–„â–€â–€ â–„â–ˆ â–„â–€ â–ˆâ–€â–„â–ˆ
2025-06-22 23:25:05,551 - whatsapp - WARNING - _read_output:109 - Failed to parse JSON from Node.js: â–ˆâ–ˆâ–ˆâ–ˆâ–ˆâ–ˆâ–ˆâ–„â–ˆ  â–ˆ â–„â–€â–€â–ˆâ–„ â–„â–€â–€â–€â–€ â–ˆâ–„ â–„â–„â–„ â–€â–ˆ â–ˆâ–ˆâ–„â–ˆ â–€â–ˆâ–ˆâ–ˆ â–€ â–„â–ˆ â–„â–„â–„ â–€â–€â–ˆâ–ˆâ–ˆ
2025-06-22 23:25:05,551 - whatsapp - WARNING - _read_output:109 - Failed to parse JSON from Node.js: â–ˆ â–„â–„â–„â–„â–„ â–ˆâ–ˆâ–ˆâ–ˆ  â–€â–ˆâ–ˆâ–ˆâ–„â–ˆâ–„ â–€â–„â–ˆ â–ˆ â–ˆâ–„â–ˆ â–ˆ â–€â–€ â–ˆâ–€â–„â–ˆâ–€â–ˆâ–€â–ˆâ–€ â–ˆâ–ˆ â–ˆâ–„â–ˆ â–€ â–ˆâ–ˆâ–ˆ
2025-06-22 23:25:05,551 - whatsapp - WARNING - _read_output:109 - Failed to parse JSON from Node.js: â–ˆ â–ˆ   â–ˆ â–ˆâ–€â–ˆ â–ˆâ–„â–„â–€â–€â–ˆâ–€â–ˆ â–„ â–€ â–€ â–„ â–„  â–„â–„â–€â–€â–€â–ˆ â–ˆâ–„â–€ â–ˆâ–„â–„â–€â–€â–„   â–„â–„â–„ â–€â–„â–ˆ
2025-06-22 23:25:05,551 - whatsapp - WARNING - _read_output:109 - Failed to parse JSON from Node.js: â–ˆ â–ˆâ–„â–„â–„â–ˆ â–ˆ â–„â–„â–€â–€â–ˆâ–ˆ    â–„â–€ â–€â–ˆâ–€â–€  â–ˆâ–ˆâ–€â–ˆ   â–ˆ â–„  â–ˆ â–„â–€â–„â–€â–„â–„ â–€ â–€â–€â–„â–„â–ˆâ–€â–ˆ
2025-06-22 23:25:05,551 - whatsapp - WARNING - _read_output:109 - Failed to parse JSON from Node.js: â–ˆâ–„â–„â–„â–„â–„â–„â–„â–ˆâ–„â–„â–ˆâ–ˆâ–ˆâ–ˆâ–„â–ˆâ–„â–„â–ˆâ–„â–„â–„â–„â–„â–ˆâ–ˆâ–ˆâ–ˆâ–ˆâ–ˆâ–ˆâ–ˆâ–„â–ˆâ–ˆâ–„â–ˆâ–ˆâ–ˆâ–„â–„â–ˆâ–„â–„â–ˆâ–ˆâ–„â–ˆâ–„â–„â–„â–„â–„â–„â–„â–ˆâ–ˆâ–ˆ
2025-06-22 23:25:05,551 - whatsapp - WARNING - _read_output:109 - Failed to parse JSON from Node.js: 
2025-06-22 23:25:05,551 - whatsapp - INFO - _handle_event:160 - QR code received for WhatsApp authentication
2025-06-22 23:25:05,553 - whatsapp - INFO - _handle_qr_code:83 - QR code received for WhatsApp authentication
2025-06-22 23:25:05,583 - whatsapp - INFO - _handle_qr_code:244 - QR code received for WhatsApp authentication
2025-06-22 23:25:22,975 - whatsapp - WARNING - _read_output:109 - Failed to parse JSON from Node.js: [INFO] Received SIGINT, shutting down gracefully
2025-06-22 23:25:22,977 - whatsapp - WARNING - _read_output:109 - Failed to parse JSON from Node.js: [INFO] Shutting down WhatsApp client...
2025-06-22 23:25:26,363 - whatsapp - INFO - stop:155 - Stopping WhatsApp integration...
2025-06-22 23:25:26,364 - whatsapp - INFO - stop_monitoring:102 - Connection monitoring stopped
2025-06-22 23:25:26,364 - whatsapp - INFO - stop:302 - Stopping WhatsApp client...
2025-06-22 23:25:26,364 - whatsapp - WARNING - _send_command:214 - Cannot send command logout: client not running
2025-06-22 23:25:26,364 - whatsapp - INFO - stop:338 - WhatsApp client stopped
2025-06-22 23:25:26,367 - whatsapp - INFO - stop:168 - WhatsApp integration stopped
