2025-06-22 23:47:53,966 - whatsapp - INFO - __init__:49 - WhatsApp manager initialized
2025-06-22 23:47:53,966 - whatsapp - INFO - initialize:58 - Initializing WhatsApp components...
2025-06-22 23:47:53,966 - whatsapp - INFO - __init__:44 - WhatsApp client initialized
2025-06-22 23:47:53,966 - whatsapp - INFO - __init__:55 - Authentication handler initialized
2025-06-22 23:47:53,966 - whatsapp - INFO - __init__:67 - Connection manager initialized
2025-06-22 23:47:53,966 - whatsapp - INFO - __init__:36 - Message listener initialized
2025-06-22 23:47:53,966 - whatsapp - INFO - __init__:38 - Message sender initialized
2025-06-22 23:47:53,991 - whatsapp - INFO - initialize:81 - WhatsApp components initialized successfully
2025-06-22 23:47:56,276 - whatsapp - INFO - start:123 - Starting WhatsApp integration...
2025-06-22 23:47:56,276 - whatsapp - INFO - start_monitoring:88 - Connection monitoring started
2025-06-22 23:47:56,276 - whatsapp - INFO - start:65 - Starting WhatsApp Web.js client...
2025-06-22 23:47:56,282 - whatsapp - INFO - start:92 - WhatsApp client process started
2025-06-22 23:47:56,286 - whatsapp - INFO - start:136 - WhatsApp integration started successfully
2025-06-22 23:47:56,286 - whatsapp - INFO - _attempt_reconnection:177 - Attempting reconnection (attempt 1/5)
2025-06-22 23:47:56,286 - whatsapp - INFO - stop:322 - Stopping WhatsApp client...
2025-06-22 23:47:56,286 - whatsapp - WARNING - _send_command:234 - Cannot send command logout: client not running
2025-06-22 23:47:56,596 - whatsapp - WARNING - _read_output:109 - Failed to parse JSON from Node.js: [INFO] Starting WhatsApp client...
2025-06-22 23:48:00,524 - whatsapp - INFO - stop:358 - WhatsApp client stopped
2025-06-22 23:48:02,531 - whatsapp - INFO - start:65 - Starting WhatsApp Web.js client...
2025-06-22 23:48:02,533 - whatsapp - INFO - start:92 - WhatsApp client process started
2025-06-22 23:48:02,533 - whatsapp - INFO - _attempt_reconnection:191 - Reconnection attempt initiated
2025-06-22 23:48:02,838 - whatsapp - WARNING - _read_output:109 - Failed to parse JSON from Node.js: [INFO] Starting WhatsApp client...
2025-06-22 23:48:06,627 - whatsapp - WARNING - _read_output:109 - Failed to parse JSON from Node.js: [ERROR] Failed to start client: Protocol error (Runtime.callFunctionOn): Execution context was destroyed.
